package com.datayes.util

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.quarkus.logging.Log

private val objectMapper = ObjectMapper().registerKotlinModule()

/**
 * 检查 JSON 对象中的所有值是否都为 null
 *
 * @param json JSON 字符串`
 * @return 如果所有值都为 null 则返回 true，否则返回 false
 */
fun isAllJsonValueNull(json: String): Boolean {
    try {
        // 将 JSON 字符串解析为 JsonNode
        val jsonNode = objectMapper.readTree(json)

        // 如果不是对象节点，返回 false
        if (!jsonNode.isObject) {
            return false
        }

        // 遍历所有字段，检查是否都为 null
        val fields = jsonNode.fields()
        while (fields.hasNext()) {
            val (_, value) = fields.next()
            // 如果有任何非 null 值，返回 false
            if (!value.isNull) {
                return false
            }
        }

        // 所有值都为 null
        return true
    } catch (e: Exception) {
        // JSON 解析错误，返回 false
        return false
    }
}

/**
 * Combines multiple list results into a single flattened list
 * Each result is expected to be a JSON array string
 */
fun mergeJsonListResults(results: List<String>): String {
    // If there's only one result, return it directly
    if (results.size == 1) {
        return results[0]
    }

    // Process each result to extract items from JSON arrays
    val allItems = mutableListOf<String>()

    for (result in results) {
        // Skip empty results or non-array results
        if (result.isBlank() || !result.trim().startsWith("[") || !result.trim().endsWith("]")) {
            Log.warn("Invalid list result format: $result")
            continue
        }

        // Extract items from the array
        // Remove the outer brackets
        val content = result.trim().removeSurrounding("[", "]").trim()
        if (content.isBlank()) {
            continue // Empty array
        }

        // Split the content by objects (items between { and })
        val itemPattern = "\\{[^{}]*}".toRegex()
        val items = itemPattern.findAll(content).map { it.value }.filter { !isAllJsonValueNull(it) }.toList()

        // Add all items to the combined list
        allItems.addAll(items)
    }

    // Combine all items into a single JSON array
    return if (allItems.isEmpty()) {
        "[]" // Return empty array if no items found
    } else {
        "[\n  ${allItems.joinToString(",\n  ")}\n]"
    }
}