package com.datayes.quarkus.rest

import io.quarkus.test.junit.QuarkusTest
import io.restassured.RestAssured.given
import org.hamcrest.CoreMatchers.`is`
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.Test

@QuarkusTest
class ExamplePromptResourceIT {

    @Test
    fun `test getExamplePrompt with valid tableId`() {
        // Given
        val tableId = 1614L

        // When/Then
        given()
            .pathParam("tableId", tableId)
            .`when`()
            .get("/api/example-prompt/{tableId}")
            .then()
            .statusCode(200)
            .body("prompt", notNullValue())
    }

    @Test
    fun `test getExamplePrompt with invalid tableId`() {
        // Given
        val tableId = -1L

        // When/Then
        given()
            .pathParam("tableId", tableId)
            .`when`()
            .get("/api/example-prompt/{tableId}")
            .then()
            .statusCode(404)
            .body("message", `is`("未找到示例数据"))
    }
} 