CREATE TABLE `llm_call_log`
(
    `ID`                BIGINT(20) NOT NULL AUTO_INCREMENT,
    `M<PERSON>EL`             VARCHAR(100)       DEFAULT NULL,
    `PROMPT`            TEXT               DEFAULT NULL,
    `RESPONSE`          TEXT               DEFAULT NULL,
    `TIME_START`        TIMESTAMP NULL DEFAULT NULL,
    `TIME_END`          TIMESTAMP NULL DEFAULT NULL,
    `TIME_COST_MS`      BIGINT(20) DEFAULT NULL,
    `PROMPT_TOKENS`     INT(11) DEFAULT NULL,
    `COMPLETION_TOKENS` INT(11) DEFAULT NULL,
    `TOTAL_TOKENS`      INT(11) DEFAULT NULL,
    `ERROR_MESSAGE`     TEXT               DEFAULT NULL,
    `FINISH_REASON`     VARCHAR(50)        DEFAULT NULL,
    `CONTEXT_INFO`      TEXT               DEFAULT NULL,
    `CREATED_AT`        TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    <PERSON><PERSON>Y                 `idx_llm_call_log_time_start` (`TIME_START`),
    <PERSON><PERSON>Y                 `idx_llm_call_log_created_at` (`CREATED_AT`)
);