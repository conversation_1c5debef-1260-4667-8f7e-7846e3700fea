package com.datayes.llm

import com.datayes.model.LlmCallLog
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import java.time.LocalDateTime

/**
 * Implementation of LlmCallLogger that persists logs to the database
 */
@ApplicationScoped
class DatabaseLlmCallLogger : Llm<PERSON>allLogger {

    /**
     * Log an LLM API call to the database
     *
     * @param model The model identifier used for the call
     * @param prompt The prompt text sent to the LLM
     * @param response The response received from the LLM
     * @param timeStart The timestamp when the call started
     * @param timeEnd The timestamp when the call ended
     * @param timeCostMs The time cost of the call in milliseconds
     * @param promptTokens The number of prompt tokens used
     * @param completionTokens The number of completion tokens used
     * @param totalTokens The total number of tokens used
     * @param errorMessage Any error message that occurred during the call
     * @param finishReason The finish reason returned by the LLM
     * @param contextInfo JSON string containing additional context information
     */
    @Transactional
    override fun logLlmCall(
        model: String,
        prompt: String,
        response: String?,
        timeStart: LocalDateTime,
        timeEnd: LocalDateTime,
        timeCostMs: Long,
        promptTokens: Int?,
        completionTokens: Int?,
        totalTokens: Int?,
        errorMessage: String?,
        finishReason: String?,
        contextInfo: String?
    ) {
        try {
            val llmCallLog = LlmCallLog(
                model = model,
                prompt = prompt,
                response = response,
                timeStart = timeStart,
                timeEnd = timeEnd,
                timeCostMs = timeCostMs,
                promptTokens = promptTokens,
                completionTokens = completionTokens,
                totalTokens = totalTokens,
                errorMessage = errorMessage,
                finishReason = finishReason,
                contextInfo = contextInfo
            )
            llmCallLog.persist()
            Log.info("Logged LLM call to database with id: ${llmCallLog.id}")
        } catch (e: Exception) {
            Log.error("Failed to log LLM call to database", e)
        }
    }
}