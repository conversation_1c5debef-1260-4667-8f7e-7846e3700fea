package com.datayes.quarkus.rest

import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import java.lang.management.ManagementFactory
import java.time.Duration
import java.time.LocalDateTime

@Path("/monitor")
class MonitorResource {

    private val memoryMXBean = ManagementFactory.getMemoryMXBean()
    private val osMXBean = ManagementFactory.getOperatingSystemMXBean()
    private val threadMXBean = ManagementFactory.getThreadMXBean()
    private val runtimeMXBean = ManagementFactory.getRuntimeMXBean()

    data class JvmStatus(
        val usedMemoryMb: Long,
        val totalMemoryMb: Long,
        val maxMemoryMb: Long,
        val availableProcessors: Int,
        val threadCount: Int,
        val peakThreadCount: Int,
        val totalStartedThreadCount: Long,
        val uptime: String
    )

    @GET
    @Path("/jvm/status")
    @Produces(MediaType.APPLICATION_JSON)
    fun getJvmStatus(): Response {
        val heapUsage = memoryMXBean.heapMemoryUsage
        val uptimeMillis = runtimeMXBean.uptime
        val uptimeDuration = Duration.ofMillis(uptimeMillis)
        val uptimeFormatted = String.format(
            "%d days, %d hours, %d minutes, %d seconds",
            uptimeDuration.toDays(),
            uptimeDuration.toHoursPart(),
            uptimeDuration.toMinutesPart(),
            uptimeDuration.toSecondsPart()
        )

        val status = JvmStatus(
            usedMemoryMb = heapUsage.used / (1024 * 1024),
            totalMemoryMb = heapUsage.committed / (1024 * 1024), // More accurate term might be 'committed'
            maxMemoryMb = heapUsage.max / (1024 * 1024),
            availableProcessors = osMXBean.availableProcessors,
            threadCount = threadMXBean.threadCount,
            peakThreadCount = threadMXBean.peakThreadCount,
            totalStartedThreadCount = threadMXBean.totalStartedThreadCount,
            uptime = uptimeFormatted
        )
        return Response.ok(status).build()
    }

    @GET
    @Path("/jvm/stacktrace")
    @Produces(MediaType.TEXT_PLAIN)
    fun getJvmStackTrace(): Response {
        val threadDump = StringBuilder()
        val threadInfos = threadMXBean.dumpAllThreads(true, true)
        threadDump.append("Full thread dump Java HotSpot(TM) 64-Bit Server VM:\n")
        threadDump.append("Timestamp: ${LocalDateTime.now()}\n\n")

        for (ti in threadInfos) {
            threadDump.append("\"${ti.threadName}\" #${ti.threadId} prio=${Thread.currentThread().priority} tid=${ti.threadId} nid=0x${java.lang.Long.toHexString(ti.threadId)} ${ti.threadState}\n")
            threadDump.append("   java.lang.Thread.State: ${ti.threadState}\n")
            if (ti.lockName != null) {
                threadDump.append("        - waiting on <0x${ti.lockOwnerId}> (a ${ti.lockName})\n")
            }
            if (ti.lockOwnerName != null) {
                threadDump.append("        - locked <0x${ti.lockOwnerId}> (a ${ti.lockOwnerName})\n")
            }
            if (ti.isSuspended) {
                threadDump.append("        (suspended)\n")
            }
            if (ti.isInNative) {
                threadDump.append("        (in native)\n")
            }
            for (ste in ti.stackTrace) {
                threadDump.append("\tat ${ste.toString()}\n")
            }
            threadDump.append("\n")
        }
        return Response.ok(threadDump.toString()).build()
    }
} 