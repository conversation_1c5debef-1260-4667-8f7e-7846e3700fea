@file:Suppress("CdiManagedBeanInconsistencyInspection")

package com.datayes.quarkus.client

import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient

/**
 * PDF 表格提取客户端接口 (PDF Tables Extraction Client Interface)
 * 使用 Quarkus REST Client 实现对外部 PDF 表格提取服务的调用
 */
@Path("/pdf")
@RegisterRestClient(configKey = "pdf-tables-api")
interface RemotePdfTablesClient {

    /**
     * 提取 PDF 表格
     *
     * @param url PDF URL 参数
     * @return PDF 表格提取响应
     */
    @GET
    @Path("/tables")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun retrievePdfTables(@QueryParam("url") url: String): TablesResponse
}

/**
 * 表格提取响应数据类 (Tables Response Data Class)
 */
data class TablesResponse(
    val code: String,
    val msg: String,
    val tables: List<TableData>,
)

/**
 * 表格数据类 (Table Data Class)
 */
data class TableData(
    // val reportId: String?,
    val title: String?,
    val preContent: String,
    val tableContent: String,
    // val pageNum: Int,
    // val crossPage: Boolean,
)