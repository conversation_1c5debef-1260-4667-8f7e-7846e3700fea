package com.datayes.quarkus.config

import com.datayes.util.InMemoryLogHandler
import com.datayes.util.InMemoryLogStore
import io.quarkus.runtime.Startup
import jakarta.annotation.PostConstruct
import jakarta.enterprise.context.ApplicationScoped
import jakarta.enterprise.inject.Produces
import org.eclipse.microprofile.config.inject.ConfigProperty
import org.jboss.logging.Logger
import java.util.logging.Level
import java.util.logging.LogManager

/**
 * 日志配置类，负责初始化和管理自定义日志处理器
 */
@ApplicationScoped
class LoggingConfig {
    
    companion object {
        private val logger = Logger.getLogger(LoggingConfig::class.java)
    }
    
    @ConfigProperty(name = "quarkus.log.handler.in-memory.enable", defaultValue = "true")
    private lateinit var enabled: String
    
    @ConfigProperty(name = "quarkus.log.handler.in-memory.level", defaultValue = "INFO")
    private lateinit var level: String
    
    @ConfigProperty(name = "quarkus.log.handler.in-memory.max-size", defaultValue = "1000")
    private lateinit var maxSize: String
    
    @Produces
    fun createInMemoryLogHandler(): InMemoryLogHandler {
        val handler = InMemoryLogHandler()
        
        // 设置日志级别
        try {
            handler.level = Level.parse(level)
        } catch (e: Exception) {
            logger.warn("Invalid log level: $level, using INFO")
            handler.level = Level.INFO
        }
        
        return handler
    }
    
    /**
     * 在应用启动时，将自定义日志处理器添加到根日志记录器
     */
    @PostConstruct
    @Startup
    fun initializeLogging() {
        // 如果未启用，则直接返回
        if (enabled.equals("false", ignoreCase = true)) {
            logger.info("InMemoryLogHandler is disabled")
            return
        }
        
        try {
            // 设置 InMemoryLogStore 的最大容量
            val size = maxSize.toIntOrNull() ?: 1000
            InMemoryLogStore.setMaxSize(size)
            
            // 添加处理器到根日志记录器
            val rootLogger = LogManager.getLogManager().getLogger("")
            val handler = createInMemoryLogHandler()
            rootLogger.addHandler(handler)
            
            logger.info("InMemoryLogHandler has been registered successfully with max size: $size")
        } catch (e: Exception) {
            logger.error("Failed to register InMemoryLogHandler", e)
        }
    }
} 