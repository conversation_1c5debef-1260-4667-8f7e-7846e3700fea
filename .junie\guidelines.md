# Auto Data Item Extract - Developer Guidelines

## Build/Configuration Instructions

### Prerequisites
- JDK 21
- <PERSON><PERSON><PERSON> 2.0.21
- Gradle (wrapper included)

### Building the Project
The project uses <PERSON>radle with <PERSON><PERSON><PERSON> DSL for build configuration. The main build file is `build.gradle.kts`.

```bash
# Build the project
./gradlew build

# Run the application in development mode
./gradlew quarkusDev
```

### Configuration
- Quarkus version: 3.21.2
- The application uses Quarkus configuration properties in `src/main/resources/application.properties`
- JVM arguments for development mode are configured in the `build.gradle.kts` file:
  ```kotlin
  tasks.withType<QuarkusDev> {
      jvmArgs = listOf(
          "-Dfile.encoding=UTF-8",
          "-Dstdout.encoding=UTF-8",
          "-Dstderr.encoding=UTF-8",
          "-XX:ReservedCodeCacheSize=1g",
          "-XX:+UseCodeCacheFlushing",
          "-Xmx8g",
          "-Xms2g"
      )
  }
  ```

## Testing Information

### Running Tests
Tests can be run using Gradle:

```bash
# Run all tests
./gradlew test

# Run a specific test class
./gradlew test --tests "com.datayes.util.JsonUtilKtTest"

# Run a specific test method
./gradlew test --tests "com.datayes.util.JsonUtilKtTest.shouldReturnTrueWhenAllJsonValuesAreNull"
```

### Test Structure
- Unit tests are located in `src/test/kotlin/com/datayes/`
- Integration tests are suffixed with `IT` (e.g., `SysTableTypeIT.kt`)
- Quarkus tests are annotated with `@QuarkusTest`

### Test Patterns
1. **Naming Convention**: Tests use descriptive names with backticks:
   ```kotlin
   @Test
   fun `test method name with scenario description`() {
       // Test code
   }
   ```

2. **Structure**: Tests follow the Arrange-Act-Assert pattern:
   ```kotlin
   // --- Arrange ---
   val input = "test input"

   // --- Act ---
   val result = methodUnderTest(input)

   // --- Assert ---
   assertEquals(expected, result)
   ```

3. **Assertions**: The project primarily uses Strikt for assertions:
   ```kotlin
   // Strikt assertion
   expectThat(actual).isEqualTo(expected)

   // Multiple assertions
   expectThat(actual) {
       isNotNull()
       isEqualTo(expected)
       isLessThan(maxValue)
   }
   ```

4. **Assumptions**: For tests that depend on specific data:
   ```kotlin
   Assumptions.assumeTrue(
       condition,
       "ASSUMPTION FAILED: Descriptive message"
   )
   ```

### Adding New Tests
1. Create a new test class in the appropriate package under `src/test/kotlin/com/datayes/`
2. For unit tests, name the class after the class being tested with a `Test` suffix
3. For integration tests, use the `IT` suffix
4. Use the `@Test` annotation for test methods
5. Follow the naming and structure conventions described above
6. For Quarkus integration tests, add the `@QuarkusTest` annotation to the class

### Example Test
Here's a simple example of a test for the `isAllJsonValueNull` function:

```kotlin
@Test
fun `isAllJsonValueNull should return false when some values are not null`() {
    // --- Arrange ---
    val json = """
        {
            "TICKER_SYMBOL": "002727",
            "EXCHANGE_CD": null,
            "NEW_PUB_DATE": "2020-04-15 00:00:00",
            "END_DATE": null
        }
    """.trimIndent()

    // --- Act ---
    val result = isAllJsonValueNull(json)

    // --- Assert ---
    expectThat(result).isEqualTo(false)
}
```

## Additional Development Information

### Project Structure
- `src/main/kotlin/com/datayes/`: Main source code
- `src/main/resources/`: Configuration files and resources
- `src/test/kotlin/com/datayes/`: Test code

### Key Packages
- `com.datayes.model`: Data models and entities
- `com.datayes.llm`: LLM (Large Language Model) related code
- `com.datayes.util`: Utility classes
- `com.datayes.quarkus`: Quarkus-specific configuration

### Code Style
- The project uses Kotlin coding conventions
- Use descriptive variable and function names
- Document public APIs with KDoc comments
- Follow the existing patterns for error handling and logging

### Debugging
- The application uses Quarkus logging
- In-memory logging is available through the `InMemoryLog` utility
- A monitoring page is available at `/monitor.html` when running in development mode

### Database Access
- The project uses Hibernate ORM with Panache for database access
- Entity classes extend `PanacheEntity` or `PanacheEntityBase`
- Database configuration is in `application.properties`

#### Testing Database Access
Integration tests for database entities are suffixed with `IT` and use the `@QuarkusTest` annotation:

```kotlin
@QuarkusTest
class LlmCallLogIT {
    // Test methods
}
```

1. **Test Method Naming**: Use descriptive names with backticks that clearly indicate what is being tested:
   ```kotlin
   @Test
   fun `test findById should not throw exception and return null when id does not exist`() {
       // Test code
   }
   ```

2. **Test Structure**: Follow the Arrange-Act-Assert pattern with clear section comments:
   ```kotlin
   // --- Arrange ---
   val maxId = LlmCallLog.getEntityManager()
       .createQuery("SELECT MAX(id) FROM llm_call_log", Long::class.java)
       .singleResult ?: 0L

   // --- Act ---
   val result = LlmCallLog.findById(maxId)

   // --- Assert ---
   assertNotNull(result)
   ```

3. **Assertions**: The project supports both Strikt and JUnit assertions:

   Strikt assertions:
   ```kotlin
   // Using expectCatching for operations that might fail
   expectCatching { LlmCallLog.findById(-1L) }
       .isSuccess() // Assert no exception was thrown
       .isNull()    // Assert the return value is null
   ```

   JUnit assertions:
   ```kotlin
   assertTrue(
       result,
       "isTableList should return true for the pre-existing tableId $knownListTableId"
   )
   ```

4. **Assumptions**: For tests that depend on specific data in the database:
   ```kotlin
   // Check if the record exists before testing its properties
   val exists = SysTableType.count("tableId", tableId) > 0
   Assumptions.assumeTrue(
       exists,
       "ASSUMPTION FAILED: Record with tableId $tableId not found in the database."
   )
   ```

5. **Database Queries**: Use native SQL queries for efficient test setup:
   ```kotlin
   // Using native SQL to get the maximum ID
   val maxId = LlmCallLog.getEntityManager()
       .createQuery("SELECT MAX(id) FROM llm_call_log", Long::class.java)
       .singleResult ?: 0L
   ```

6. **Error Messages**: Include descriptive error messages in assertions and assumptions:
   ```kotlin
   assertTrue(
       count > 5,
       "The sys_table_type table should have more than 5 records, but found $count"
   )
   ```

### REST APIs
- REST endpoints are implemented using Quarkus REST
- Jackson is used for JSON serialization/deserialization
- The `jackson-module-kotlin` is included for Kotlin data class support
- Use Kotlin data classes for responses, letting the framework handle JSON serialization
- Error handling:
  - Unrecoverable errors are allowed to propagate to the default exception handler
  - Only recoverable errors are handled explicitly in the code
  - Example:
    ```kotlin
    @GET
    @Path("/data")
    fun getData(): DataResponse {
        return try {
            val data = dataService.fetchData()
            DataResponse(data = data, success = true)
        } catch (e: RecoverableException) {
            // Handle recoverable exception
            Log.warn("Recoverable error occurred", e)
            DataResponse(error = "Unable to fetch data: ${e.message}", success = false)
        }
        // Unrecoverable exceptions will be handled by the default exception handler
    }

    data class DataResponse(
        val data: Any? = null,
        val error: String? = null,
        val success: Boolean
    )
    ```
