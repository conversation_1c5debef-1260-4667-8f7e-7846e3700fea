---
trigger: always_on
---

- this project use quarkus and kotlin to build rest api
- avoid use method chaining, give each step a variable name
- write testable code, don't use mock to setup test unless it the only way
- for integration test, just use the real database/api etc, and assume that the required data is already in the database/api
- use kotlin object (not class) for grouping functions
- don't try to unit test an method/function with too much io, seperate the io and pure logic, only unit test the pure logic
- it's ok to pass database entities between layers, just make it easy to change the entities to dto if needed later
- prefer to handle exceptions at the outer most layer, just let exceptions propagate, unless when we need to continue the flow
- every exception need to been logged with stacktrace 
- use kotlin sealed class for result type, don't use null as error or special value
