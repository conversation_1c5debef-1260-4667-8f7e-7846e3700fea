---
description:
globs:
alwaysApply: false
---
# PDF智能抽取系统项目结构 (PDF Intelligent Extraction System Structure)

本项目是一个基于 Kotlin、Quarkus 和 MySQL 的 PDF 智能抽取系统，用于自动化从 PDF 文档中提取结构化数据。

## 核心包结构 (Core Package Structure)

项目代码采用分层结构，将核心业务逻辑与框架代码分离：

- [src/main/kotlin/com/datayes/model](mdc:src/main/kotlin/com/datayes/model) - 数据库实体/领域模型
- [src/main/kotlin/com/datayes/llm](mdc:src/main/kotlin/com/datayes/llm) - LLM 集成和提示词构建
- [src/main/kotlin/com/datayes/util](mdc:src/main/kotlin/com/datayes/util) - 通用工具类
- [src/main/kotlin/com/datayes/quarkus](mdc:src/main/kotlin/com/datayes/quarkus) - Quarkus 框架相关代码

## 核心组件 (Core Components)

系统的主要组件包括：

1. **数据模型 (Data Models)**
   - [SysDataAnno](mdc:src/main/kotlin/com/datayes/model/SysDataAnno.kt) - 数据源配置表实体
   - [SysColumnType](mdc:src/main/kotlin/com/datayes/model/SysColumnType.kt) - 字段配置表实体
   - [SysTableType](mdc:src/main/kotlin/com/datayes/model/SysTableType.kt) - 表类型配置实体
   - [LlmResponse](mdc:src/main/kotlin/com/datayes/model/LlmResponse.kt) - LLM 响应数据存储实体

2. **LLM 集成 (LLM Integration)**
   - [DoubaoLlm](mdc:src/main/kotlin/com/datayes/llm/Doubao.kt) - Doubao LLM 客户端集成
   - [LlmPromptBuilder](mdc:src/main/kotlin/com/datayes/llm/LlmPromptBuilder.kt) - LLM 提示词构建

3. **REST API (REST Endpoints)**
   - [PdfResource](mdc:src/main/kotlin/com/datayes/quarkus/rest/PdfResource.kt) - PDF 处理 REST API
