package com.datayes.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanionBase
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*

/**
 * 字段配置表实体 (Column Configuration Entity)
 */
@Entity(name = "sys_column_type")
data class SysColumnType(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "COLUMN_ID")
    var columnId: Long? = null,

    @Column(name = "IS_EXTRACTED")
    var isExtracted: Long? = null,

    @Column(name = "COLUMN_TYPE")
    var columnType: Long? = null,

    @Column(name = "TASK_ID")
    var taskId: Long? = null,

    @Column(name = "COLUMN_NAME")
    var columnName: String? = null,

    // delete at 2025-06-25
    // @Column(name = "COLUMN_NAME_EN")
    // var columnNameEn: String? = null,

    @Column(name = "DEC")
    var description: String? = null,

    @Column(name = "RULE")
    var rule: String? = null,

    @Column(name = "TABLE_NAME")
    var tableName: String? = null,

    @Column(name = "EXT_TYPE")
    var extType: String? = null,

    @Column(name = "IS_KEY")
    var isKey: Int? = null

) : PanacheEntityBase {

    companion object : PanacheCompanionBase<SysColumnType, Long> {

        // find by task id
        fun findByTaskId(taskId: Long): List<SysColumnType> {
            // 使用实体属性名 (field name) 构造 (HQL) 查询，避免 "Could not interpret path expression" 异常
            return find("taskId = ?1", taskId).list()
        }
    }
}

data class PromptColumnMetadata(
    val columnName: String,
    val columnNameEn: String?,
    val rule: String?,
)

/**
 * Maps a list of SysColumnType entities to a list of PromptColumnMetadata objects.
 *
 * Each SysColumnType in the list is transformed into a PromptColumnMetadata object by extracting
 * the `columnName`, `columnNameEn`, and either `rule` or `description` (if `rule` is null).
 *
 * @return A list of PromptColumnMetadata objects derived from the SysColumnType entities.
 */
fun List<SysColumnType>.promptColumnMetadata(): List<PromptColumnMetadata> {
    val promptColumnMetadata = this.map { column ->
        PromptColumnMetadata(
            columnName = column.columnName ?: "",
            columnNameEn = null,
            rule = column.rule ?: column.description,
        )
    }
    return promptColumnMetadata
}
