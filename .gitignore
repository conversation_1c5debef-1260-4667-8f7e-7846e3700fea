#Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
release.properties
.flattened-pom.xml

# Eclipse
.project
.classpath
.settings/
bin/
build/
target/
.gradle

# IntelliJ
.idea
*.ipr
*.iml
*.iws

# NetBeans
nb-configuration.xml

# Visual Studio Code
.vscode
.factorypath

# OSX
.DS_Store

# Vim
*.swp
*.swo

# patch
*.orig
*.rej

# Local environment
.env

# Plugin directory
/.quarkus/cli/plugins/
# TLS Certificates
.certs/


# windsurf rules
.windsurfrules
.aider*
.kotlin

# Python (uv/pip)
scripts/schema-extractor/.venv/
scripts/schema-extractor/__pycache__/
scripts/schema-extractor/*.pyc
scripts/schema-extractor/*.pyo
scripts/schema-extractor/*.pyd
scripts/schema-extractor/.Python

# Generated schema files
scripts/schema-extractor/database_schema.json