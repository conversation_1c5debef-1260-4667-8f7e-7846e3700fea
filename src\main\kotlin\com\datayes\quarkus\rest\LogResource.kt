package com.datayes.quarkus.rest

import com.datayes.util.InMemoryLogStore
import jakarta.ws.rs.DefaultValue
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response

/**
 * REST API 资源，用于获取内存中存储的日志
 */
@Path("/api/logs")
class LogResource {
    
    /**
     * 获取最近的日志条目
     * 
     * @param limit 要获取的最大日志条目数, 默认为 100
     * @return 日志条目列表
     */
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    fun getLogs(@QueryParam("limit") @DefaultValue("100") limit: Int): Response {
        val logs = InMemoryLogStore.getLogs(limit)
        return Response.ok(logs).build()
    }
    
    /**
     * 获取最近的日志条目，以HTML格式返回
     * 用于在monitor页面中通过htmx展示
     * 
     * @param limit 要获取的最大日志条目数, 默认为 100
     * @return HTML格式的日志条目
     */
    @GET
    @Path("/html")
    @Produces(MediaType.TEXT_HTML)
    fun getLogsHtml(@QueryParam("limit") @DefaultValue("100") limit: Int): Response {
        val logs = InMemoryLogStore.getLogs(limit)
        
        val htmlBuilder = StringBuilder()
        if (logs.isEmpty()) {
            htmlBuilder.append("<div class=\"log-entry\">No logs available</div>")
        } else {
            // Reverse the list to show newest logs first
            logs.reversed().forEach { log ->
                // 替换HTML特殊字符，防止XSS
                val safeLog = log.replace("<", "&lt;").replace(">", "&gt;")
                
                // 检测不同日志级别并添加不同的CSS类
                val logClass = when {
                    safeLog.contains(" ERROR ") -> "log-error"
                    safeLog.contains(" WARN ") -> "log-warn"
                    safeLog.contains(" INFO ") -> "log-info"
                    safeLog.contains(" DEBUG ") -> "log-debug"
                    safeLog.contains(" TRACE ") -> "log-trace"
                    else -> ""
                }
                
                htmlBuilder.append("<div class=\"log-entry ${logClass}\">").append(safeLog).append("</div>")
            }
        }
        
        return Response.ok(htmlBuilder.toString()).build()
    }
} 