package com.datayes.quarkus.client

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isNotEmpty

/**
 * (Integration Test) for (RemotePdfTablesClient)
 * 直接调用实际(PDF表格提取服务)，验证接口和数据流。
 * 注意：需保证 pdf-tables-api 指向可访问的服务。
 */
@QuarkusTest
class RemotePdfTablesClientIT {

    @Inject
    @RestClient
    lateinit var client: RemotePdfTablesClient

    @Test
    fun `should extract tables from remote pdf`() {
        val pdfUrl = "/pipeline/report/2024-05-10/20240510_1be618c92cb4a9176b1931604f04a9cca6f9561b0.pdf"

        val result = client.retrievePdfTables(url = pdfUrl)

        println("PDF Tables extraction result: $result")

        // Verify response structure
        expectThat(result.code).isEqualTo("1")
        expectThat(result.msg).isEqualTo("success")
        expectThat(result.tables).isNotEmpty()

        // Verify table content if available
        if (result.tables.isNotEmpty()) {
            val firstTable = result.tables.first()
            expectThat(firstTable.tableContent).isNotEmpty()
        }
    }
}