package com.datayes.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanionBase
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.Instant
import java.time.LocalDateTime

/**
 * LLM API Call Log Entity
 * Stores information about calls to the Doubao LLM API
 */
@Entity(name = "llm_call_log")
data class LlmCallLog(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "model", length = 100)
    var model: String? = null,

    @Column(name = "prompt", columnDefinition = "TEXT")
    var prompt: String? = null,

    @Column(name = "response", columnDefinition = "TEXT")
    var response: String? = null,

    @Column(name = "time_start")
    var timeStart: LocalDateTime? = null,

    @Column(name = "time_end")
    var timeEnd: LocalDateTime? = null,

    @Column(name = "time_cost_ms")
    var timeCostMs: Long? = null,

    @Column(name = "prompt_tokens")
    var promptTokens: Int? = null,

    @Column(name = "completion_tokens")
    var completionTokens: Int? = null,

    @Column(name = "total_tokens")
    var totalTokens: Int? = null,

    @Column(name = "error_message", columnDefinition = "TEXT")
    var errorMessage: String? = null,

    @Column(name = "finish_reason", length = 50)
    var finishReason: String? = null,

    // SELECT * FROM llm_call_log WHERE CONTEXT_INFO->'$.annoId' = '598425ac9c40fc3dfecea4f6';
    @Column(name = "context_info", columnDefinition = "TEXT") // 或者使用 "JSON" 如果你的数据库支持
    var contextInfo: String? = null, // 用于存储 JSON 字符串

    @Column(name = "created_at")
    var createdAt: Instant = Instant.now()
) : PanacheEntityBase {
    companion object : PanacheCompanionBase<LlmCallLog, Long> {
        fun findByTimeRange(startTime: LocalDateTime, endTime: LocalDateTime): List<LlmCallLog> {
            return find("timeStart >= ?1 and timeEnd <= ?2", startTime, endTime).list()
        }

        fun findWithError(): List<LlmCallLog> {
            return find("errorMessage is not null").list()
        }
    }
}