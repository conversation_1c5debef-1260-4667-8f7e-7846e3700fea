quarkus.http.port                                   = 5101

quarkus.datasource.db-kind                          = mysql
quarkus.datasource.username                         = ace_sjzx6_rw
quarkus.datasource.password                         = E3%7(*bvf&*veN
quarkus.datasource.jdbc.url                         = ************************************************************************
quarkus.datasource.db-version                       = 5.7.0
quarkus.datasource.jdbc.max-size                    = 16

quarkus.hibernate-orm.database.generation           = none
quarkus.hibernate-orm.log.sql                       = true
quarkus.hibernate-orm.log.format-sql                = false

quarkus.log.console.enable                          = true
quarkus.log.console.level                           = INFO
quarkus.log.console.format                          = %d{HH:mm:ss} %-5p [%c{1}.%M](%F:%L) %s%e%n
#quarkus.log.console.format                          = %d{HH:mm:ss} %-5p [%c{1}.%M](%25F:%4L) %s%e%n

quarkus.log.handler.in-memory.enable                = true
quarkus.log.handler.in-memory.level                 = INFO
quarkus.log.handler.in-memory.max-size              = 1000

quarkus.rest-client.pdf-content-api.url             = http://10.21.138.90:8000
quarkus.rest-client.pdf-content-api.scope           = jakarta.inject.Singleton
quarkus.rest-client.pdf-content-api.connect-timeout = 10000
quarkus.rest-client.pdf-content-api.read-timeout    = 120000

quarkus.rest-client.pdf-tables-api.url             = http://************:9016
quarkus.rest-client.pdf-tables-api.scope           = jakarta.inject.Singleton
quarkus.rest-client.pdf-tables-api.connect-timeout = 10000
quarkus.rest-client.pdf-tables-api.read-timeout    = 120000

# OpenAPI/Swagger UI Configuration
quarkus.smallrye-openapi.info-title                = Auto Data Item Extract API
quarkus.smallrye-openapi.info-version              = 1.0.0
quarkus.smallrye-openapi.info-description          = REST API for automated data extraction from PDF documents using LLM
quarkus.smallrye-openapi.info-contact-name         = DataYes Team
quarkus.swagger-ui.always-include                  = true
quarkus.swagger-ui.path                            = /q/swagger-ui
