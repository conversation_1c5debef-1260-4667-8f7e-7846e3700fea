# windsurf rule file for PDF智能抽取系统 (PDF Intelligent Extraction System)
# 项目简介、代码结构、编码风格规范

project:
  name: PDF智能抽取系统 (PDF Intelligent Extraction System)
  summary: |
    本项目基于 Kotlin、Quarkus 和 MySQL，旨在实现自动化从 PDF 文档中抽取结构化数据。系统通过 REST API 集成 PDF 文本提取与 LLM (Large Language Model) 智能抽取，结合数据库配置，实现高效的数据处理与存储。项目采用极简、数据驱动的开发风格，核心逻辑与框架解耦，强调可维护性和可扩展性。

structure:
  description: |
    代码采用分层结构，核心业务逻辑与数据结构全部放在 com.datayes 包下，所有与 Quarkus 框架相关的代码集中在 com.datayes.quarkus 子包。数据库实体采用 JPA 注解风格，直接作为领域模型。所有核心算法、数据处理、业务流程均为纯 Kotlin 实现，便于测试和维护。
  layout:
    - src/
      - main/
        - kotlin/
          - com/
            - datayes/
              - model/         # 数据库实体/领域模型 (data class with JPA annotations)
              - extraction/    # 核心算法与流程 (object 组织)
              - util/          # 工具 object
              - quarkus/
                - resource/    # REST API (XXXResource)
                - repository/  # Quarkus ORM/Panache
                - config/      # Quarkus 配置
      - resources/
        - application.properties
        - logback.xml
      - test/
        - kotlin/
          - com/
            - datayes/
              - extraction/    # 纯 Kotlin 测试，无 mock
              - util/

coding_style:
  summary: |
    本项目遵循极简、数据驱动、Kotlin 原生风格，核心逻辑与框架解耦。所有数据结构用 data class 定义，数据库实体采用 JPA 注解风格。所有函数集合用 object 组织，避免不必要的 OOP。测试直接针对纯 Kotlin 逻辑，无需 mock。优先使用 Kotlin 标准库，始终追求简单、可维护、可扩展。
  rules:
    - 所有数据库实体 (entity) 使用 data class，配合 @Entity、@Id、@GeneratedValue、@Column 等 JPA 注解，继承 PanacheEntityBase，字段允许 var、可空类型和默认值。
    - 业务逻辑、工具函数统一用 object 组织，模拟静态方法 (static function)，避免 class unless 必须。
    - 领域模型允许直接用数据库实体，如需 DTO，单独定义并提供转换函数。
    - 只用 data class、object、顶级函数，极少用 class/OOP，除非有充分理由（如多态、复杂状态机等）。
    - 所有流程、数据流均以数据结构为核心，函数只做数据变换。
    - 核心逻辑不依赖任何框架 (framework)，只用 Kotlin 标准库。
    - Quarkus 相关代码全部集中在 com.datayes.quarkus 包，REST API 只做参数转发与结果返回。
    - 测试直接针对纯 Kotlin 逻辑，无需 mock，鼓励写可测试的纯函数。
    - 命名风格遵循 Kotlin 官方建议，包名小写，类名/对象名大驼峰，字段名小驼峰。
    - 优先简单、数据驱动、标准库优先，避免过度设计和复杂性。

examples:
  entity_example: |
    @Entity(name = "extraction_item")
    data class ExtractionItem(
        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        var id: Long? = null,
        @Column(name = "RESULT_ID")
        var resultId: Long? = null,
        @Column(name = "FIELD1")
        var field1: String? = null,
        @Column(name = "CREATE_TIME")
        var createTime: LocalDateTime? = LocalDateTime.now(),
        @Column(name = "UPDATE_TIME")
        var updateTime: LocalDateTime? = LocalDateTime.now()
    ) : PanacheEntityBase()
  object_example: |
    object PdfExtraction {
        fun extractTextFromPdfUrl(pdfUrl: String): String {
            // 纯 Kotlin 实现或调用外部 API
            return ""
        }
    }
  resource_example: |
    @Path("/api/pdf")
    object PdfResource {
        @POST
        @Path("/extract-text")
        @Produces(MediaType.TEXT_PLAIN)
        fun extractText(pdfUrl: String): Response {
            val text = PdfExtraction.extractTextFromPdfUrl(pdfUrl)
            return Response.ok(text).build()
        }
    }
