---
description:
globs:
alwaysApply: false
---
# PDF智能抽取系统编码风格规范 (PDF Intelligent Extraction System Coding Style)

本项目遵循极简、数据驱动、Kotlin 原生风格，强调核心逻辑与框架解耦。以下是项目中采用的编码风格规范：

## 数据结构定义 (Data Structure Definition)

- 所有数据库实体 (entity) 使用 `data class`，配合 JPA 注解，继承 `PanacheEntityBase`
- 字段允许可空类型 (`null`) 和默认值
- 示例：

```kotlin
@Entity(name = "extraction_item")
data class ExtractionItem(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    @Column(name = "RESULT_ID")
    var resultId: Long? = null,
    @Column(name = "FIELD1")
    var field1: String? = null,
    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime? = LocalDateTime.now(),
    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime? = LocalDateTime.now()
) : PanacheEntityBase()
```

## 业务逻辑组织 (Business Logic Organization)

- 业务逻辑、工具函数统一用 `object` 组织，模拟静态方法 (static function)
- 避免不必要的 OOP 和类继承
- 示例：

```kotlin
object PdfExtraction {
    fun extractTextFromPdfUrl(pdfUrl: String): String {
        // 纯 Kotlin 实现或调用外部 API
        return ""
    }
}
```

## REST API 风格 (REST API Style)

- REST API 只做参数转发与结果返回
- 示例：

```kotlin
@Path("/api/pdf")
object PdfResource {
    @POST
    @Path("/extract-text")
    @Produces(MediaType.TEXT_PLAIN)
    fun extractText(pdfUrl: String): Response {
        val text = PdfExtraction.extractTextFromPdfUrl(pdfUrl)
        return Response.ok(text).build()
    }
}
```

## 通用编码规则 (General Coding Rules)

1. 只用 `data class`、`object`、顶级函数，极少用 class/OOP
2. 所有流程、数据流均以数据结构为核心，函数只做数据变换
3. 核心逻辑不依赖任何框架 (framework)，只用 Kotlin 标准库
4. 测试直接针对纯 Kotlin 逻辑，无需 mock
5. 命名风格遵循 Kotlin 官方建议，包名小写，类名/对象名大驼峰，字段名小驼峰
