#!/usr/bin/env python3
"""
Database Schema Extractor for auto-data-item-extract project.

This script reads database connection info from application.properties
and extracts the complete schema information for Claude Code usage.
"""

import json
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, parse_qs
import mysql.connector
from mysql.connector import Error


class DatabaseSchemaExtractor:
    def __init__(self, config_path: str = "../../src/main/resources/application.properties"):
        self.config_path = Path(config_path)
        self.connection = None
        self.db_config = {}
        
    def parse_application_properties(self) -> Dict[str, str]:
        """Parse application.properties file to extract database configuration."""
        config = {}
        
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
        with open(self.config_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
        
        return config
    
    def extract_db_connection_info(self) -> Dict[str, Any]:
        """Extract database connection parameters from application.properties."""
        config = self.parse_application_properties()
        
        # Parse JDBC URL
        jdbc_url = config.get('quarkus.datasource.jdbc.url', '')
        if not jdbc_url.startswith('jdbc:mysql://'):
            raise ValueError(f"Unsupported JDBC URL format: {jdbc_url}")
        
        # Remove jdbc:mysql:// prefix and parse
        mysql_url = jdbc_url.replace('jdbc:mysql://', 'mysql://')
        parsed = urlparse(mysql_url)
        
        # Extract connection parameters
        db_config = {
            'host': parsed.hostname,
            'port': parsed.port or 3306,
            'database': parsed.path.lstrip('/'),
            'user': config.get('quarkus.datasource.username', ''),
            'password': config.get('quarkus.datasource.password', ''),
        }
        
        # Parse additional parameters from URL
        query_params = parse_qs(parsed.query)
        if 'characterEncoding' in query_params:
            db_config['charset'] = query_params['characterEncoding'][0]
        
        return db_config
    
    def connect_database(self):
        """Establish database connection."""
        try:
            self.db_config = self.extract_db_connection_info()
            self.connection = mysql.connector.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                database=self.db_config['database'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                charset=self.db_config.get('charset', 'utf8'),
                autocommit=True
            )
            print(f"Connected to MySQL database: {self.db_config['database']} at {self.db_config['host']}:{self.db_config['port']}")
            
        except Error as e:
            print(f"Error connecting to MySQL database: {e}")
            sys.exit(1)
    
    def get_tables_info(self) -> List[Dict[str, Any]]:
        """Get information about all tables in the database."""
        cursor = self.connection.cursor(dictionary=True)
        
        query = """
        SELECT 
            TABLE_NAME,
            TABLE_TYPE,
            ENGINE,
            TABLE_COMMENT,
            CREATE_TIME,
            UPDATE_TIME,
            TABLE_ROWS,
            DATA_LENGTH,
            INDEX_LENGTH
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = %s
        ORDER BY TABLE_NAME
        """
        
        cursor.execute(query, (self.db_config['database'],))
        tables = cursor.fetchall()
        cursor.close()
        
        return tables
    
    def get_columns_info(self, table_name: str) -> List[Dict[str, Any]]:
        """Get detailed column information for a specific table."""
        cursor = self.connection.cursor(dictionary=True)
        
        query = """
        SELECT 
            COLUMN_NAME,
            ORDINAL_POSITION,
            COLUMN_DEFAULT,
            IS_NULLABLE,
            DATA_TYPE,
            CHARACTER_MAXIMUM_LENGTH,
            NUMERIC_PRECISION,
            NUMERIC_SCALE,
            COLUMN_TYPE,
            COLUMN_KEY,
            EXTRA,
            COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
        ORDER BY ORDINAL_POSITION
        """
        
        cursor.execute(query, (self.db_config['database'], table_name))
        columns = cursor.fetchall()
        cursor.close()
        
        return columns
    
    def get_indexes_info(self, table_name: str) -> List[Dict[str, Any]]:
        """Get index information for a specific table."""
        cursor = self.connection.cursor(dictionary=True)
        
        query = """
        SELECT 
            INDEX_NAME,
            COLUMN_NAME,
            NON_UNIQUE,
            SEQ_IN_INDEX,
            INDEX_TYPE,
            INDEX_COMMENT
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
        ORDER BY INDEX_NAME, SEQ_IN_INDEX
        """
        
        cursor.execute(query, (self.db_config['database'], table_name))
        indexes = cursor.fetchall()
        cursor.close()
        
        return indexes
    
    def get_foreign_keys_info(self, table_name: str) -> List[Dict[str, Any]]:
        """Get foreign key information for a specific table."""
        cursor = self.connection.cursor(dictionary=True)
        
        query = """
        SELECT 
            kcu.CONSTRAINT_NAME,
            kcu.COLUMN_NAME,
            kcu.REFERENCED_TABLE_NAME,
            kcu.REFERENCED_COLUMN_NAME,
            rc.DELETE_RULE,
            rc.UPDATE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
        ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
        AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
        WHERE kcu.TABLE_SCHEMA = %s 
        AND kcu.TABLE_NAME = %s 
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        ORDER BY kcu.CONSTRAINT_NAME, kcu.ORDINAL_POSITION
        """
        
        cursor.execute(query, (self.db_config['database'], table_name))
        foreign_keys = cursor.fetchall()
        cursor.close()
        
        return foreign_keys
    
    def extract_complete_schema(self) -> Dict[str, Any]:
        """Extract complete database schema information."""
        from datetime import datetime
        
        schema = {
            'database_info': {
                'name': self.db_config['database'],
                'host': self.db_config['host'],
                'port': self.db_config['port'],
                'extracted_at': datetime.now().isoformat()
            },
            'tables': {}
        }
        
        # Get all tables
        tables = self.get_tables_info()
        
        for table in tables:
            table_name = table['TABLE_NAME']
            print(f"Processing table: {table_name}")
            
            # Get columns, indexes, and foreign keys for each table
            columns = self.get_columns_info(table_name)
            indexes = self.get_indexes_info(table_name)
            foreign_keys = self.get_foreign_keys_info(table_name)
            
            # Group indexes by index name
            grouped_indexes = {}
            for idx in indexes:
                idx_name = idx['INDEX_NAME']
                if idx_name not in grouped_indexes:
                    grouped_indexes[idx_name] = {
                        'name': idx_name,
                        'type': idx['INDEX_TYPE'],
                        'unique': not bool(idx['NON_UNIQUE']),
                        'comment': idx['INDEX_COMMENT'],
                        'columns': []
                    }
                grouped_indexes[idx_name]['columns'].append(idx['COLUMN_NAME'])
            
            schema['tables'][table_name] = {
                'info': {
                    'name': table_name,
                    'type': table['TABLE_TYPE'],
                    'engine': table['ENGINE'],
                    'comment': table['TABLE_COMMENT'],
                    'rows': table['TABLE_ROWS'],
                    'data_length': table['DATA_LENGTH'],
                    'index_length': table['INDEX_LENGTH'],
                    'created_at': str(table['CREATE_TIME']) if table['CREATE_TIME'] else None,
                    'updated_at': str(table['UPDATE_TIME']) if table['UPDATE_TIME'] else None
                },
                'columns': columns,
                'indexes': list(grouped_indexes.values()),
                'foreign_keys': foreign_keys
            }
        
        return schema
    
    def save_schema_to_file(self, schema: Dict[str, Any], output_file: str = "database_schema.json"):
        """Save schema information to JSON file."""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(schema, f, indent=2, ensure_ascii=False, default=str)
        print(f"Schema saved to: {output_file}")
    
    def print_schema_summary(self, schema: Dict[str, Any]):
        """Print a summary of the extracted schema."""
        print(f"\n=== Database Schema Summary ===")
        print(f"Database: {schema['database_info']['name']}")
        print(f"Host: {schema['database_info']['host']}:{schema['database_info']['port']}")
        print(f"Tables found: {len(schema['tables'])}")
        
        print(f"\n=== Tables Overview ===")
        for table_name, table_info in schema['tables'].items():
            columns_count = len(table_info['columns'])
            indexes_count = len(table_info['indexes'])
            fk_count = len(table_info['foreign_keys'])
            rows = table_info['info']['rows'] or 0
            
            print(f"  {table_name}: {columns_count} columns, {indexes_count} indexes, {fk_count} FKs, ~{rows} rows")
    
    def close_connection(self):
        """Close database connection."""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("Database connection closed.")


def main():
    """Main execution function."""
    extractor = DatabaseSchemaExtractor()
    
    try:
        # Connect to database
        extractor.connect_database()
        
        # Extract complete schema
        schema = extractor.extract_complete_schema()
        
        # Save to file
        extractor.save_schema_to_file(schema)
        
        # Print summary
        extractor.print_schema_summary(schema)
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    finally:
        extractor.close_connection()


if __name__ == "__main__":
    main()
