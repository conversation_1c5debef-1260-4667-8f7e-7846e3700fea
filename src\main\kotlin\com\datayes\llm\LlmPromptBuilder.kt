package com.datayes.llm

import com.datayes.model.PromptColumnMetadata
import com.datayes.quarkus.client.TableData
import com.datayes.quarkus.service.PdfTextExtractor

/**
 * LLM提示构建对象 (LLM Prompt Builder Object)
 * 负责构建用于数据提取的LLM提示
 */
object LlmPromptBuilder {

    // 从示例数据构建示例部分
    fun buildExamplesSection(content: String, relationFieldsList: List<String>): String? {
        if (content.isBlank() || relationFieldsList.isEmpty()) {
            return null
        }

        val formattedRelationFields = if (relationFieldsList.size == 1) {
            relationFieldsList.single()
        } else {
            """[${relationFieldsList.joinToString(", ")}]"""
        }

        return """
           |示例数据:
           |原文：
           |$content
           | 
           |提取结果：
           |$formattedRelationFields
        """.trimMargin()
    }

    /**
     * 从表格数据构建提示
     *
     * @param tableData 表格数据对象，包含标题、前置内容和表格内容
     * @param promptColumnMetadata 列信息列表，包含列名和规则
     * @param isList 是否返回列表结果
     * @return 构建的LLM提示
     */
    fun generatePromptForTable(
        tableData: TableData,
        promptColumnMetadata: List<PromptColumnMetadata>,
        isList: Boolean = false,
    ): List<String> {
        // 检查表格内容长度是否超过阈值（8*1024字符）
        // Although LLM has a 32K token limit, when input is very long the output tends to be long as well,
        // so 8K is used as a safe threshold to ensure reliable responses
        val maxContentLength = 8 * 1024

        // 构建表格标题和前置内容部分
        val headerContent = buildString {
            append("表格标题: ${tableData.title}\n\n")

            if (tableData.preContent.isNotBlank()) {
                append("表格前置内容:\n${tableData.preContent}\n\n")
            }

            append("表格内容:\n")
        }

        // 如果表格内容长度小于阈值，直接生成单个提示
        if (tableData.tableContent.length <= maxContentLength) {
            val combinedContent = headerContent + tableData.tableContent
            return listOf(generatePromptForText(combinedContent, promptColumnMetadata, isList))
        }

        // 将表格内容按行分割
        val tableLines = tableData.tableContent.split("\n")

        // 使用splitPdfContent函数分割内容
        val maxChunkSize = maxContentLength - headerContent.length
        val contentChunks = PdfTextExtractor.splitPdfContent(tableLines, maxChunkSize)

        // 为每个分割后的内容块生成提示
        return contentChunks.map { chunk ->
            val chunkContent = headerContent + chunk.joinToString("\n")
            generatePromptForText(chunkContent, promptColumnMetadata, isList)
        }
    }

    /**
     * 构建数据提取提示
     *
     * @param pdfContent PDF文档内容
     * @param promptColumnMetadata 列信息列表，包含列名和规则
     * @return 构建的LLM提示
     */
    fun generatePromptForText(
        pdfContent: String,
        promptColumnMetadata: List<PromptColumnMetadata>,
        isList: Boolean = false,
    ): String {
        // 生成字段规则部分
        val columnRules = promptColumnMetadata.joinToString("\n") { info ->
            buildString {
                val columnNameEn = info.columnNameEn
                if (columnNameEn.isNullOrBlank()) {
                    append("字段名称: ${info.columnName}\n")
                } else {
                    append("字段名称: ${info.columnName}(${info.columnNameEn})\n")
                }
                if (!info.rule.isNullOrBlank()) {
                    append("提取规则: ${info.rule}\n")
                }
            }.trimEnd()
        }

        // 生成JSON示例部分
        val jsonExample = promptColumnMetadata.joinToString(",\n") { info ->
            "\"${info.columnNameEn ?: info.columnName}\": 提取的值或null"
        }

        // 根据isList生成不同的提示和JSON格式
        val introLine = if (isList) {
            "请从以下PDF文档内容中提取指定字段的数据，返回匹配条件的多个对象列表。"
        } else {
            "请从以下PDF文档内容中提取指定字段的数据。"
        }

        // 为列表模式生成属性字符串，缩进4个空格
        val listPropertiesStr = promptColumnMetadata.joinToString(",\n") { info ->
            "    \"${info.columnNameEn ?: info.columnName}\": 提取的值或null"
        }

        // 为非列表模式生成属性字符串，缩进2个空格
        val singlePropertiesStr = promptColumnMetadata.joinToString(",\n") { info ->
            "  \"${info.columnNameEn ?: info.columnName}\": 提取的值或null"
        }

        val resultFormat = if (isList) {
            """
            |请严格按照以下格式返回结果，注意返回的是对象数组：
            |<result>
            |[
            |  {
            |$listPropertiesStr
            |  },
            |  {
            |$listPropertiesStr
            |  },
            |  ...更多对象
            |]
            |</result>
            |
            |注意：请只返回<result></result>标签中的内容，不要添加任何总结或解释。
            |如果找不到任何匹配的数据，则返回空数组 []。
            """.trimMargin("|")
        } else {
            """
            |请严格按照以下格式返回结果：
            |<result>
            |{
            |$singlePropertiesStr
            |}
            |</result>
            |
            |注意：请只返回<result></result>标签中的内容，不要添加任何总结或解释。
            """.trimMargin("|")
        }

        // 使用多行字符串拼接提示词
        return """
            |$introLine
            |对于每个字段，请严格按照提供的规则进行提取。
            |只返回JSON格式的结果，并将结果放在<result></result>标签中。
            |
            |需要提取的字段及规则：
            |$columnRules
            |
            |特别说明：如果某个字段的值是数字，但包含英文逗号 (comma)，请将其作为字符串 (string) 返回。例如："BASE_SHARES": "595,258,710.00"。否则会导致 (JSON) 格式错误。
            |
            |$resultFormat
            |
            |PDF文档内容：
            |$pdfContent
        """.trimMargin("|")
    }

    /**
     * Build a prompt for extracting information with context from previous extractions
     */
    fun generatePromptWithPreviousValuesForText(
        content: String,
        promptColumnMetadata: List<PromptColumnMetadata>,
        previousValues: Map<String, String>,
        isList: Boolean = false,
    ): String {
        val basePrompt = generatePromptForText(content, promptColumnMetadata, isList)

        // Add context from previous extractions
        val contextInfo = if (previousValues.isNotEmpty()) {
            """
            |Previously extracted values:
            |${previousValues.entries.joinToString("\n") { (key, value) -> "- $key: $value" }}
            |
            |Please use these values as context and only update them if you find more accurate or additional information in the current content.
            """.trimMargin()
        } else {
            ""
        }

        return if (contextInfo.isBlank()) {
            basePrompt
        } else {
            """
            |$basePrompt
            |
            |$contextInfo
            """.trimMargin()
        }
    }


    /**
     * 构建表格选择提示
     *
     * 生成用于让LLM选择最匹配目标表格的提示文本
     *
     * @param tableNameCn 表名（中文）
     * @param tableName 表名（英文）
     * @param tableDec 表格描述
     * @param columnInfos 需要提取的列信息
     * @param batchIndex 当前批次索引
     * @param batchesSize 总批次数
     * @param tableBatch 当前批次的表格数据
     * @return 构建好的提示文本
     */
    fun buildTableSelectionPrompt(
        tableDesc: String,
        columnInfos: List<PromptColumnMetadata>,
        batchIndex: Int,
        batchesSize: Int,
        tableBatch: List<IndexedValue<TableData>>,
    ): String = buildString {
        append("任务：从以下PDF表格中，选择哪一个表格最符合我们需要提取的目标数据。\n\n")
        append("目标表格信息：\n")
        append("- 表格描述：$tableDesc\n")
        // append("- 表格名称（中文）：${tableNameCn ?: "未提供"}\n")
        // append("- 表格名称（英文）：${tableName ?: "未提供"}\n")
        // if (!tableDec.isNullOrBlank()) {
        //     append("- 表格描述：$tableDec\n")
        // }
        append("- 需要提取的字段：${columnInfos.joinToString(", ") { it.columnName }}\n\n")

        append("PDF中检测到的表格（批次${batchIndex + 1}/${batchesSize}）：\n")
        tableBatch.forEach { (originalIndex, table) ->
            append("表格 #${originalIndex + 1}:\n")
            append("- 标题: ${table.title ?: "无标题"}\n")
            if (table.preContent.isNotBlank()) {
                val truncatedPreContent = if (table.preContent.length > 200)
                    "${table.preContent.take(200)}..."
                else
                    table.preContent
                append("- 前置内容摘要: $truncatedPreContent\n")
            }
            append("- 表格内容预览: ${table.tableContent.lines().take(3).joinToString(" | ")}")
            if (table.tableContent.lines().size > 3) {
                append(" ... (更多行省略)")
            }
            append("\n\n")
        }

        append("请分析每个表格的标题、内容与目标表格的相关性，并选择最匹配的表格。")
        append("如果有多个表格匹配度相似，也可以选择多个表格。")
        append("请以JSON格式返回结果，原始表格索引${if (tableBatch.size > 1) "数组" else ""}：\n")
        append("```json\n")
        append("{\n")
        append("  \"selectedTableIndices\": [原始表格索引], // 原始表格索引，不是从1开始的新索引\n")
        append("  \"explanation\": \"选择理由\"\n")
        append("}\n")
        append("```\n")
        append("如果所有表格都不匹配，请返回空数组。")
    }
}
