package com.datayes.llm

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.quarkus.logging.Log
import java.net.ProxySelector
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.time.Instant
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicInteger

data class DoubaoTokenUsage(
    val promptTokens: Int,
    val completionTokens: Int,
    val totalTokens: Int,
)

// 添加时间戳的令牌使用记录
data class TimestampedTokenUsage(
    val timestamp: Instant,
    val tokenUsage: DoubaoTokenUsage,
)

data class DoubaoMessage(
    val role: String,
    val content: String,
)

data class DoubaoResponse(
    val content: String,
    val tokenUsage: DoubaoTokenUsage,
    val finishReason: String? = null,
)

fun DoubaoResponse.extractTagContent(tag: String = "result"): String {
    val resultPattern = "<${tag}>(.*?)</${tag}>".toRegex(RegexOption.DOT_MATCHES_ALL)
    val resultMatch = resultPattern.find(this.content)
    return resultMatch?.groupValues?.get(1)?.trim() ?: ""
}

// https://confluence.datayes.com/pages/viewpage.action?pageId=194056652
// ep-20240813154652-rxhpf Doubao-pro-32k
// ep-20240717024829-kdgzx Doubao-lite-32k

// Doubao-1.5-pro-256k
// ep-20250205111944-cks4v
//
// Doubao-1.5-lite-32k
// ep-20250205111920-c8tj6
//
// Doubao-1.5-pro-32k
// ep-20250205111857-5d5k6
//
// Doubao-1.5-vision-pro-32k
// ep-20250205111834-4jg8d
// 令牌使用统计对象
object DoubaoTokenUsageStats {
    // 总计使用的令牌数
    private val totalPromptTokens = AtomicInteger(0)
    private val totalCompletionTokens = AtomicInteger(0)
    private val totalTokens = AtomicInteger(0)

    // 保存带时间戳的令牌使用记录
    private val tokenUsageHistory = ConcurrentLinkedQueue<TimestampedTokenUsage>()

    // 获取总计令牌使用情况
    fun getTotalTokenUsage(): DoubaoTokenUsage = DoubaoTokenUsage(
        promptTokens = totalPromptTokens.get(),
        completionTokens = totalCompletionTokens.get(),
        totalTokens = totalTokens.get()
    )

    // 获取最近一段时间的令牌使用情况
    private fun getRecentTokenUsage(seconds: Long): DoubaoTokenUsage {
        val cutoffTime = Instant.now().minusSeconds(seconds)
        return tokenUsageHistory
            .filter { it.timestamp.isAfter(cutoffTime) }
            .fold(DoubaoTokenUsage(0, 0, 0)) { acc, item ->
                DoubaoTokenUsage(
                    promptTokens = acc.promptTokens + item.tokenUsage.promptTokens,
                    completionTokens = acc.completionTokens + item.tokenUsage.completionTokens,
                    totalTokens = acc.totalTokens + item.tokenUsage.totalTokens
                )
            }
    }

    // 获取最近一天的令牌使用情况
    fun getLastDayTokenUsage(): DoubaoTokenUsage = getRecentTokenUsage(86400)

    // 获取最近一小时的令牌使用情况
    fun getLastHourTokenUsage(): DoubaoTokenUsage = getRecentTokenUsage(3600)

    // 获取最近15分钟的令牌使用情况
    fun getLast15MinTokenUsage(): DoubaoTokenUsage = getRecentTokenUsage(900)

    // 上次打印日志的时间
    private var lastLogTime = Instant.now()

    // 每分钟打印一次令牌使用统计日志
    fun logTokenUsageStats() {
        val now = Instant.now()
        if (now.isAfter(lastLogTime.plusSeconds(60))) {
            val totalUsage = getTotalTokenUsage()
            val last15MinUsage = getLast15MinTokenUsage()
            val lastHourUsage = getLastHourTokenUsage()
            val lastDayUsage = getLastDayTokenUsage()

            Log.info(
                """
                88a31aa6 | 令牌使用统计:
                总计: prompt=${totalUsage.promptTokens.toReadableString()}, completion=${totalUsage.completionTokens.toReadableString()}, total=${totalUsage.totalTokens.toReadableString()}
                最近15分钟: prompt=${last15MinUsage.promptTokens.toReadableString()}, completion=${last15MinUsage.completionTokens.toReadableString()}, total=${last15MinUsage.totalTokens.toReadableString()}
                最近1小时: prompt=${lastHourUsage.promptTokens.toReadableString()}, completion=${lastHourUsage.completionTokens.toReadableString()}, total=${lastHourUsage.totalTokens.toReadableString()}
                最近24小时: prompt=${lastDayUsage.promptTokens.toReadableString()}, completion=${lastDayUsage.completionTokens.toReadableString()}, total=${lastDayUsage.totalTokens.toReadableString()}
                """.trimIndent()
            )

            val promptCost = totalUsage.promptTokens / 1_000_000.0 * 0.3
            val completionCost = totalUsage.completionTokens / 1_000_000.0 * 0.6
            val totalCost = promptCost + completionCost

            Log.info("536ecf02dc1b | 总计成本: promptCost=$promptCost, completionCost=$completionCost, totalCost=$totalCost")

            lastLogTime = now
        }
    }

    // 将数字格式化为易读的字符串（添加千位分隔符）
    private fun Int.toReadableString(): String {
        return String.format("%,d", this)
    }

    // 清理旧的令牌使用记录（保留最近24小时的数据）
    private fun cleanupOldRecords() {
        val cutoffTime = Instant.now().minusSeconds(86400)
        tokenUsageHistory.removeIf { it.timestamp.isBefore(cutoffTime) }
    }

    // 更新令牌使用统计
    fun updateTokenUsage(tokenUsage: DoubaoTokenUsage) {
        totalPromptTokens.addAndGet(tokenUsage.promptTokens)
        totalCompletionTokens.addAndGet(tokenUsage.completionTokens)
        totalTokens.addAndGet(tokenUsage.totalTokens)

        // 添加带时间戳的令牌使用记录
        tokenUsageHistory.add(TimestampedTokenUsage(Instant.now(), tokenUsage))

        // 定期清理旧记录
        if (tokenUsageHistory.size % 100 == 0) {
            cleanupOldRecords()
        }
    }
}

object DoubaoLlm {

    private val API_KEY: String = System.getenv("DOUBAO_API_KEY") ?: "e1a4cb142a944da9835591b0c4326d22"

    private const val API_URL: String = "http://llm-gateway.respool2.wmcloud-stg.com/api/v3/chat/completions"

    // doubao-1.5-lite
    // private const val DEFAULT_MODEL: String = "ep-20250205111920-c8tj6"

    // Doubao-Seed-1.6
    // private const val DEFAULT_MODEL: String = "ep-20250616175451-qzrsr"

    // Doubao-Seed-1.6-flash
    private const val DEFAULT_MODEL: String = "ep-20250619095118-jw98s"

    private val httpClient = HttpClient.newBuilder().proxy(ProxySelector.of(null)).build()

    private val objectMapper = ObjectMapper().registerKotlinModule()

    /**
     * Send a prompt to the Doubao LLM API and get a response
     *
     * @param prompt The prompt text to send to the LLM
     * @param model Optional model identifier, defaults to DEFAULT_MODEL if not provided
     * @param logger Optional logger to log the API call, defaults to null
     * @param context Optional context map to provide additional information for logging
     * @return DoubaoResponse containing the LLM's response and token usage information
     */
    fun sendPrompt(
        prompt: String,
        model: String? = null,
        logger: LlmCallLogger?,
        context: Map<String, Any>? = null
    ): DoubaoResponse {
        val actualModel = model ?: DEFAULT_MODEL
        val messages = listOf(DoubaoMessage("user", prompt))
        val timeStart = LocalDateTime.now()
        var errorMessage: String? = null
        var tokenUsage: DoubaoTokenUsage? = null
        var content: String? = null
        var finishReason: String? = null
        var doubaoResponse: DoubaoResponse? = null

        try {
            // choose model by total messages content length
            val totalLength = messages.sumOf { it.content.length }
            if (totalLength > 1024 * 30) {
                throw IllegalStateException("bdf905b3d24f | Doubao API messages content length is too long")
            }

            val requestBody = mapOf(
                "model" to actualModel,
                "messages" to messages.map { msg ->
                    mapOf(
                        "role" to msg.role,
                        "content" to msg.content.replace("\"", "\\\"")
                    )
                },
                "stream" to false,
                "temperature" to 0.2,
                "max_tokens" to 12288
            )

            val writeValueAsString = objectMapper.writeValueAsString(requestBody)
            val request = HttpRequest.newBuilder()
                .uri(URI.create(API_URL))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer $API_KEY")
                .timeout(java.time.Duration.ofSeconds(60 * 5))
                .POST(HttpRequest.BodyPublishers.ofString(writeValueAsString))
                .build()

            val (response, timeCost) = measureTimeWithResult {
                Log.info("f886e0a1 | Sending request to Doubao API: $API_URL")
                val resp = httpClient.send(request, HttpResponse.BodyHandlers.ofString())
                Log.info("30e67a8c | Received response from Doubao API: ${resp.statusCode()}")
                resp
            }
            Log.info("a479ec36ac7e | Doubao API request time cost: ${timeCost / 1_000_000} ms")
            if (response.statusCode() != 200) {
                errorMessage = "HTTP Error: ${response.statusCode()} - ${response.body()}"
                Log.error("1ae72f7d56d7 | Doubao API error: ${response.statusCode()} - ${response.body()}")
                throw RuntimeException("Failed to call Doubao API")
            }

            val responseJson = objectMapper.readTree(response.body())
            val usage = responseJson.get("usage")
            tokenUsage = DoubaoTokenUsage(
                promptTokens = usage.get("prompt_tokens").asInt(),
                completionTokens = usage.get("completion_tokens").asInt(),
                totalTokens = usage.get("total_tokens").asInt()
            )

            // 更新令牌使用统计
            val start = System.currentTimeMillis()
            DoubaoTokenUsageStats.updateTokenUsage(tokenUsage)
            Log.info("b66b1ae4 | Doubao API update token usage time cost: ${System.currentTimeMillis() - start} ms")
            DoubaoTokenUsageStats.logTokenUsageStats()

            Log.info("d463e27e | Doubao API token usage: prompt_tokens=${tokenUsage.promptTokens}, completion_tokens=${tokenUsage.completionTokens}, total_tokens=${tokenUsage.totalTokens}")

            finishReason = responseJson.get("choices").get(0).get("finish_reason")?.asText()
            content = responseJson.get("choices").get(0).get("message").get("content").asText()
            if (finishReason == "length") {
                errorMessage = "Doubao API response finish_reason is length"
                throw RuntimeException("Doubao API response finish_reason is length")
            }

            doubaoResponse = DoubaoResponse(
                content = content ?: "",
                tokenUsage = tokenUsage,
                finishReason = finishReason
            )
        } catch (e: Exception) {
            if (errorMessage == null) {
                errorMessage = e.message ?: "Unknown error"
            }
            throw e
        } finally {
            val timeEnd = LocalDateTime.now()
            val timeCostMs = java.time.Duration.between(timeStart, timeEnd).toMillis()

            // 将context转换为JSON字符串
            val contextJson = if (context != null) objectMapper.writeValueAsString(context) else null

            // Log the call if a logger is provided
            logger?.logLlmCall(
                model = actualModel,
                prompt = prompt,
                response = content,
                timeStart = timeStart,
                timeEnd = timeEnd,
                timeCostMs = timeCostMs,
                promptTokens = tokenUsage?.promptTokens,
                completionTokens = tokenUsage?.completionTokens,
                totalTokens = tokenUsage?.totalTokens,
                errorMessage = errorMessage,
                finishReason = finishReason,
                contextInfo = contextJson
            )
        }

        // This should never be null unless an exception is thrown, in which case we won't reach here
        return doubaoResponse ?: throw IllegalStateException("Failed to get response from Doubao API")
    }

    /**
     * 测量代码块执行时间并返回结果
     *
     * @param block 要执行的代码块
     * @return Pair<结果, 执行时间(纳秒)>
     */
    private inline fun <T> measureTimeWithResult(block: () -> T): Pair<T, Long> {
        val startTime = System.nanoTime()
        val result = block()
        val endTime = System.nanoTime()
        return Pair(result, endTime - startTime)
    }
}
