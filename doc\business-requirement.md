# PDF智能抽取系统需求文档 (Detailed Requirements Document for PDF Intelligent Extraction System)

## 一、项目背景与目标 (Project Background & Objectives)

本项目旨在开发一个全新的后端应用 (backend application)，自动化地从 PDF 文档中抽取结构化数据。系统将结合 MySQL 数据库 (MySQL Database) 配置、REST API (REST API) 文本提取、大语言模型 (LLM, Large Language Model) 智能抽��等能力，实现高效的数据处理与存储，提升数据处理自动化和准确性。

---

## 二、技术选型 (Technology Stack)

- 编程语言：Kotlin
- 构建工具：Maven
- 后端框架：Quarkus
- 数据库：MySQL
- AI服务：通过 HTTP API (HTTP API) 调用 LLM，POST 请求 (POST request)，响应为 XML 包裹的 JSON

---

## 三、核心业务流程 (Core Business Workflow)

1. **读取配置 (Read Config)**
   - 从 MySQL 数据库的 sys_data_anno 和 sys_column_type 两张表读取数据源和字段配置。
     - sys_data_anno：描述数据表、原文地址、关联字段等信息。
     - sys_column_type：描述每个字段的类型、抽取规则、是否抽取等详细属性。

2. **PDF 文本提取 (PDF Text Extraction)**
   - 通过 REST API (REST API)，传入 PDF 的 URL (string param)，返回 PDF 的纯文本内容 (plain text)。

3. **构建 LLM Prompt 并调用 (Build LLM Prompt & Call)**
   - 结合 PDF 文本和配置，自动生成 LLM 的 prompt，将 Kotlin 数据类 (Kotlin data class) 源码 (source code) 附加到 prompt 中。
   - 要求 LLM 以 JSON 格式 (JSON format) 返回抽取结果，并包裹在 <result> XML 标签 (XML tag) 内。

4. **解析与存储 (Parse & Store)**
   - 从 LLM 响应 (LLM response) 中提取 <result> 标签内的 JSON 字符串 (JSON string)，并用 JSON 反序列化器 (JSON deserializer) 转换为 Kotlin 数据类列表 (list of Kotlin data class)。
   - 将整体抽取任务结果和每个数据类对象分别存入 extraction_result 和 extraction_item 两张表。

---

## 四、数据库设计 (Database Design)

### 1. sys_data_anno 表 (数据源配置表 Data Source Configuration)

| 字段名 (Field)     | 类型 (Type)     | 说明 (Description)                                 |
|--------------------|-----------------|----------------------------------------------------|
| ID                 | bigint(20)      | 主键，自增 (Primary Key, auto-increment)           |
| TABLE_ID           | bigint(20)      | 关联数据表ID (Related data table ID)               |
| RELATION_FIELDS    | varchar(500)    | 关联字段 (Relation fields)                         |
| ANNO_ID            | varchar(500)    | 标注ID (Annotation ID)                             |
| DB                 | varchar(50)     | 数据库名 (Database name)                           |
| REPORT_ADDRESS     | varchar(500)    | 原文S3地址 (Original S3 address)                   |
| CON_DATA           | varchar(500)    | 表内关联字段名称，多个用逗号分隔 (Connection fields)|

### 2. sys_column_type 表 (字段配置表 Column Configuration)

| 字段名 (Field)     | 类型 (Type)     | 说明 (Description)                                 |
|--------------------|-----------------|----------------------------------------------------|
| ID                 | bigint(20)      | 主键，自增 (Primary Key, auto-increment)           |
| TABLE_ID           | bigint(20)      | 表ID (Table ID)                                    |
| COLUMN_ID          | bigint(20)      | 字段ID (Column ID)                                 |
| IS_EXTRACTED       | bigint(20)      | 是否抽取 (1-是，0-否) (Is extracted)               |
| COLUMN_TYPE        | bigint(20)      | 字段类型 (1-文本，2-数值，3-日期) (Column type)    |
| TASK_ID            | bigint(20)      | 任务ID (Task ID)                                   |
| COLUMN_NAME        | varchar(100)    | 字段中文名 (Column name CN)                        |
| COLUMN_NAME_EN     | varchar(100)    | 字段英文名 (Column name EN)                        |
| DEC                | varchar(500)    | 字段描述 (Description)                             |
| RULE               | varchar(500)    | 抽取规则 (Extraction rule)                         |
| TABLE_NAME         | varchar(100)    | 表名 (Table name)                                  |
| EXT_TYPE           | tinyint(4)      | 抽取类型 (Extraction type)                         |
| IS_KEY             | tinyint(4)      | 是否唯一字段 (1-是，0-否) (Is unique)              |

### 3. extraction_result 表 (抽取结果主表 Extraction Result)

| 字段名 (Field)   | 类型 (Type)   | 说明 (Description)                          |
|------------------|--------------|---------------------------------------------|
| ID               | bigint(20)   | 主键，自增 (Primary Key, auto-increment)    |
| TABLE_ID         | bigint(20)   | 关联 sys_data_anno.TABLE_ID (Foreign key)   |
| PDF_URL          | varchar(500) | 原始PDF文件的URL (Original PDF URL)         |
| EXTRACTED_JSON   | text         | 提取结果的JSON字符串 (Extracted JSON string)|
| RAW_TEXT         | longtext     | PDF原文文本 (Raw text from PDF)             |
| LLM_PROMPT       | longtext     | 发送给大模型的prompt内容 (LLM prompt)       |
| STATUS           | int(11)      | 处理状态，如0-失败，1-成功 (Status)         |
| ERROR_MSG        | varchar(1000)| 错误信息 (Error message, if any)            |
| CREATE_TIME      | datetime     | 创建时间 (Create time)                      |
| UPDATE_TIME      | datetime     | 更新时间 (Update time)                      |

### 4. extraction_item 表 (抽取结果明细表 Extraction Item)

| 字段名 (Field)         | 类型 (Type)     | 说明 (Description)                                   |
|------------------------|-----------------|------------------------------------------------------|
| ID                     | bigint(20)      | 主键，自增 (Primary Key, auto-increment)             |
| RESULT_ID              | bigint(20)      | 关联 extraction_result.ID (Foreign key reference)     |
| FIELD1                 | ...             | Kotlin 数据类的字段1 (Field1 of Kotlin data class)   |
| FIELD2                 | ...             | Kotlin 数据类的字段2 (Field2 of Kotlin data class)   |
| ...                    | ...             | 其他字段 (Other fields as needed)                    |
| CREATE_TIME            | datetime        | 创建时间 (Create time)                               |
| UPDATE_TIME            | datetime        | 更新时间 (Update time)                               |

> 注：extraction_item 的字段需根据您的 Kotlin 数据类实际结构进行定义。

---

## 五、接口与交互 (API & Interaction)

- **PDF 文本提取接口 (PDF Text Extraction API)：**
  - 输入参数：PDF 文件的 URL (string param)
  - 输出：PDF 的纯文本内容 (plain text)

- **LLM 调用接口 (LLM Call API)：**
  - 方式：POST 请求 (POST request)
  - 请求体：包含 prompt（含 PDF 文本、配置、Kotlin 数据类源码）
  - 响应：JSON 格式数据，包裹在 <result> XML 标签 (XML tag) 内

---

## 六、关键业务规则 (Key Business Rules)

- LLM 响应格式要求：JSON 数据必须包裹在 <result> 标签内，便于后续解析。
- extraction_result 与 extraction_item 通过 RESULT_ID 建立一对多关联 (one-to-many relationship)。
- 系统需支持异常处理 (error handling) 和状态追踪 (status tracking)。
- 支持后续扩展多种数据类或多种抽取任务。

---

## 七、异常与日志 (Error Handling & Logging)

- 对于 PDF 提取、LLM 调用等环节，需记录详细异常信息 (error message)。
- 处理状态 (status) 字段用于标记每条数据的处理进度与结果。
- 重要操作与异常需写入日志 (logging)，便于后续排查和优化。

---

## 八、未来可扩展性 (Future Scalability)

- 数据库表结构支持灵活扩展，便于后续增加字段或支持更多类型的数据抽取。
- LLM 服务接口采用可配置 (configurable) 设计，便于切换不同 AI 服务商或模型。
- 支持多任务并发处理 (concurrent processing) 和批量操作 (batch operation)。
