package com.datayes.quarkus.service

import com.datayes.model.SysColumnType
import io.quarkus.logging.Log

/**
 * Column Type Service
 * 提供sys_column_type相关的查询服务
 */
object ColumnTypeService {

    /**
     * 根据extraction_task的ID查询相关的sys_column_type列表
     *
     * @param extractionTaskId extraction_task的ID
     * @return 相关的SysColumnType列表
     */
    fun findColumnTypesByExtractionTaskId(extractionTaskId: Long): List<SysColumnType> {
        Log.info("6a8c4d2e | 查询extraction_task ID: $extractionTaskId 的column types")
        
        return try {
            val columnTypes = SysColumnType.find("TASK_ID = ?1", extractionTaskId).list()
            Log.info("b9e7f3a1 | 找到 ${columnTypes.size} 个column types")
            columnTypes
        } catch (e: Exception) {
            Log.error("4f5c8b6d | 查询column types失败: ${e.message}", e)
            emptyList()
        }
    }

    /**
     * 根据extraction_task的ID查询相关的sys_column_type，并按照指定条件过滤
     *
     * @param extractionTaskId extraction_task的ID
     * @param isExtracted 是否已提取的过滤条件，null表示不过滤
     * @param isKey 是否为关键字段的过滤条件，null表示不过滤
     * @return 过滤后的SysColumnType列表
     */
    fun findColumnTypesByExtractionTaskId(
        extractionTaskId: Long,
        isExtracted: Long? = null,
        isKey: Int? = null
    ): List<SysColumnType> {
        Log.info("a1d3f5b7 | 查询extraction_task ID: $extractionTaskId 的column types，过滤条件: isExtracted=$isExtracted, isKey=$isKey")
        
        return try {
            val queryBuilder = StringBuilder("TASK_ID = ?1")
            val params = mutableListOf<Any>(extractionTaskId)
            
            isExtracted?.let {
                queryBuilder.append(" AND IS_EXTRACTED = ?${params.size + 1}")
                params.add(it)
            }
            
            isKey?.let {
                queryBuilder.append(" AND IS_KEY = ?${params.size + 1}")
                params.add(it)
            }
            
            val columnTypes = SysColumnType.find(queryBuilder.toString(), *params.toTypedArray()).list()
            Log.info("c2e8f4a6 | 找到 ${columnTypes.size} 个符合条件的column types")
            columnTypes
        } catch (e: Exception) {
            Log.error("7d9a1b3e | 查询column types失败: ${e.message}", e)
            emptyList()
        }
    }

    /**
     * 根据extraction_task的ID和table_name查询相关的sys_column_type列表
     *
     * @param extractionTaskId extraction_task的ID
     * @param tableName 表名
     * @return 相关的SysColumnType列表
     */
    fun findColumnTypesByExtractionTaskIdAndTableName(
        extractionTaskId: Long,
        tableName: String
    ): List<SysColumnType> {
        Log.info("e3f6a9c2 | 查询extraction_task ID: $extractionTaskId, table_name: $tableName 的column types")
        
        return try {
            val columnTypes = SysColumnType.find(
                "TASK_ID = ?1 AND TABLE_NAME = ?2", 
                extractionTaskId, 
                tableName
            ).list()
            Log.info("f5b8d1a4 | 找到 ${columnTypes.size} 个匹配的column types")
            columnTypes
        } catch (e: Exception) {
            Log.error("8c2e5f7a | 查询column types失败: ${e.message}", e)
            emptyList()
        }
    }
}