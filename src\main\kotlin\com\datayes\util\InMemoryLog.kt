package com.datayes.util

import java.text.SimpleDateFormat
import java.util.Date
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.logging.Formatter
import java.util.logging.Handler
import java.util.logging.LogRecord

/**
 * 自定义日志格式化器，匹配应用中配置的日志格式
 * 格式: %d{mm:ss} %-5p [%c{1}.%M](%F:%L) %s%e%n
 */
class InMemoryLogFormatter : Formatter() {

    private val dateFormat = SimpleDateFormat("mm:ss")

    override fun format(record: LogRecord): String {
        val date = dateFormat.format(Date(record.millis))

        // 获取日志级别并左对齐到5个字符
        val level = String.format("%-5s", record.level.name)

        // 获取类名的最后一部分(简短类名)
        val className = record.sourceClassName?.substringAfterLast('.') ?: "Unknown"
        val methodName = record.sourceMethodName ?: "Unknown"

        // 获取源文件名和行号 (如果可用)
        val sourceFile = record.sourceClassName?.substringAfterLast('.')?.plus(".kt") ?: "Unknown"
        val lineNumber = 0 // Java Logging API 不提供行号信息

        // 构建完整的日志消息
        val message = formatMessage(record)

        // 异常信息处理
        val throwable = if (record.thrown != null) {
            val sw = java.io.StringWriter()
            val pw = java.io.PrintWriter(sw)
            record.thrown.printStackTrace(pw)
            pw.close()
            "\n" + sw.toString()
        } else {
            ""
        }

        // 返回格式化的日志字符串
        return "$date $level [$className.$methodName]($sourceFile:$lineNumber) $message$throwable\n"
    }
}

/**
 * 自定义日志处理器，将日志记录到 InMemoryLogStore
 * 可用于在应用内存中保存最近的日志条目，便于通过 API 查询
 */
class InMemoryLogHandler : Handler() {

    init {
        // 使用自定义格式化器
        formatter = InMemoryLogFormatter()
    }

    override fun publish(record: LogRecord) {
        if (!isLoggable(record)) {
            return
        }

        try {
            // 使用配置的 formatter 格式化日志记录
            val formattedLog = formatter.format(record)

            // 将格式化后的日志添加到 InMemoryLogStore
            InMemoryLogStore.appendLog(formattedLog)
        } catch (e: Exception) {
            // 防止日志处理过程中的异常影响主应用
            System.err.println("Error in InMemoryLogHandler: ${e.message}")
        }
    }

    override fun flush() {
        // 内存存储不需要刷新
    }

    override fun close() {
        // 不需要特殊的关闭操作
    }
}

/**
 * 一个单例对象，用于在内存中存储最近的日志条目。
 * 使用 ConcurrentLinkedQueue 保证线程安全。
 */
object InMemoryLogStore {

    // 使用线程安全的队列来存储格式化后的日志字符串
    private val logQueue = ConcurrentLinkedQueue<String>()

    // 限制存储的最大日志条目数，可根据需要调整
    private const val DEFAULT_MAX_SIZE = 1000
    private var maxSize = DEFAULT_MAX_SIZE // 允许后续通过配置修改 (如果需要)

    /**
     * 添加一条日志条目到队列。
     * 如果队列已满，则移除最旧的条目。
     *
     * @param logEntry 格式化后的日志字符串。
     */
    fun appendLog(logEntry: String) {
        // 在添加前检查大小，确保不会超过 maxSize
        while (logQueue.size >= maxSize) {
            logQueue.poll() // 移除队列头部的元素 (最旧的)
        }
        logQueue.add(logEntry) // 在队列尾部添加新元素
    }

    /**
     * 获取存储的最新日志条目。
     *
     * @param limit 要获取的最大日志条目数，默认为队列的最大容量。
     * @return 最近的日志条目列表。
     */
    fun getLogs(limit: Int = maxSize): List<String> {
        // 使用 takeLast 获取队列尾部的元素 (最新的)
        // 注意：对于 ConcurrentLinkedQueue，直接迭代或 toList() 可能更高效
        // 但 takeLast 对于获取最新的 N 个元素是清晰的意图表达
        // 对于非常大的队列和频繁调用，考虑性能影响
        return logQueue.toList().takeLast(limit.coerceAtMost(maxSize)) // 转为列表后取最后 N 个
    }

    /**
     * (可选) 允许设置最大队列大小。
     * 如果需要通过配置设置，可以添加此方法。
     */
    fun setMaxSize(newSize: Int) {
        if (newSize > 0) {
            maxSize = newSize
            // (可选) 如果新大小小于当前大小，可能需要立即清理超出部分
            while (logQueue.size > maxSize) {
                logQueue.poll()
            }
        }
    }

    /**
     * (可选) 清除所有日志。
     */
    fun clearLogs() {
        logQueue.clear()
    }
}