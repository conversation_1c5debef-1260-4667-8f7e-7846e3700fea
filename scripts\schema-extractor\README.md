# Database Schema Extractor

A Python script that extracts complete database schema information from your MySQL database for Claude Code usage.

## Features

- **Automatic Configuration**: Reads database connection info from `application.properties`
- **Complete Schema Extraction**: Tables, columns, indexes, foreign keys, and metadata
- **Structured Output**: JSON format optimized for Claude Code consumption
- **Comprehensive Information**: Column types, constraints, table statistics, and relationships

## Requirements

- Python 3.8+
- uv (for dependency management)
- Network access to your MySQL database

## Installation & Setup

The script is already set up as a uv project with dependencies managed automatically.

## Usage

### Basic Usage

```bash
# From the script directory
cd scripts/schema-extractor
uv run main.py
```

### Output Files

The script generates:
- `database_schema.json`: Complete schema information in JSON format
- Console output: Summary of extracted tables and statistics

### Configuration

The script automatically reads database connection info from:
```
../../src/main/resources/application.properties
```

No additional configuration is needed - it uses the same database settings as your Quarkus application.

## Schema Output Structure

The generated JSON includes:

```json
{
  "database_info": {
    "name": "acedb",
    "host": "************",
    "port": 3390,
    "extracted_at": "2025-01-06T10:30:00"
  },
  "tables": {
    "table_name": {
      "info": {
        "name": "table_name",
        "type": "BASE TABLE",
        "engine": "InnoDB",
        "comment": "Table description",
        "rows": 1000,
        "data_length": 16384,
        "index_length": 0
      },
      "columns": [
        {
          "COLUMN_NAME": "id",
          "DATA_TYPE": "bigint",
          "IS_NULLABLE": "NO",
          "COLUMN_KEY": "PRI",
          "EXTRA": "auto_increment",
          "COLUMN_COMMENT": "Primary key"
        }
      ],
      "indexes": [
        {
          "name": "PRIMARY",
          "type": "BTREE",
          "unique": true,
          "columns": ["id"]
        }
      ],
      "foreign_keys": [
        {
          "CONSTRAINT_NAME": "fk_table_ref",
          "COLUMN_NAME": "ref_id",
          "REFERENCED_TABLE_NAME": "referenced_table",
          "REFERENCED_COLUMN_NAME": "id"
        }
      ]
    }
  }
}
```

## Benefits for Claude Code

This schema information helps Claude Code:
- Understand your database structure without querying it
- Generate accurate SQL queries and data access code
- Suggest appropriate data types and constraints
- Understand relationships between tables
- Provide better code completion and suggestions

## Error Handling

The script includes comprehensive error handling for:
- Missing configuration files
- Database connection failures
- Invalid JDBC URL formats
- Network connectivity issues

## Security

- Uses the same credentials as your application
- No additional authentication required
- Connection details are read from existing configuration
- Sensitive information is not logged or exposed

## Troubleshooting

**Connection Issues:**
- Verify your application.properties has correct database settings
- Ensure the database server is accessible from your machine
- Check firewall settings if connecting to remote database

**Permission Issues:**
- The database user needs SELECT permissions on INFORMATION_SCHEMA
- Standard application database user should work fine

**Path Issues:**
- Script expects to be run from `scripts/schema-extractor/` directory
- Ensure `application.properties` exists at `../../src/main/resources/application.properties`