@file:Suppress("CdiManagedBeanInconsistencyInspection")

package com.datayes.quarkus.client

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient

/**
 * PDF 内容提取客户端接口 (PDF Content Extraction Client Interface)
 * 使用 Quarkus REST Client 实现对外部 PDF 内容提取服务的调用
 */
@Path("/api/v1")
@RegisterRestClient(configKey = "pdf-content-api")
interface RemotePdfContentClient {

    /**
     * 提取 PDF 内容
     *
     * @param request 包含 PDF URL 的请求对象
     * @return PDF 内容提取响应
     */
    @POST
    @Path("/extract-content")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    fun extractContent(request: ExtractContentRequest): List<String>
}

/**
 * 内容提取请求数据类 (Content Extraction Request Data Class)
 */
data class ExtractContentRequest(@JsonProperty("pdf_url") val pdfUrl: String)
