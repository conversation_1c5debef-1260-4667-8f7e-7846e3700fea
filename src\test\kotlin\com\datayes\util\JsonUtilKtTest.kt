package com.datayes.util

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo

class JsonUtilKtTest {

    /**
     * Test class for the `mergeJsonListResults` function in JsonUtilKt.
     *
     * The `mergeJsonListResults` function takes a list of JSON string results, validates
     * the list elements, removes null-value objects, and merges them into a single JSON array string.
     */

    @Test
    fun `mergeJsonListResults should return single result when list has one valid JSON`() {
        val input = listOf("""[{"key":"value"}]""")
        val expectedOutput = """[{"key":"value"}]"""
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should return empty JSON array for empty input list`() {
        val input = emptyList<String>()
        val expectedOutput = """[]"""
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should handle multiple valid JSON arrays with non-null values`() {
        val input = listOf(
            """[{"key1":"value1"}]""",
            """[{"key2":"value2"}]"""
        )
        val expectedOutput = """
        |[
        |  {"key1":"value1"},
        |  {"key2":"value2"}
        |]
        """.trimMargin()
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should filter out JSON objects with all null values`() {
        val input = listOf(
            """[{"key1":null}, {"key2":"value2"}]""",
            """[{"key3":null}]"""
        )
        val expectedOutput = """
        |[
        |  {"key2":"value2"}
        |]
        """.trimMargin()
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should handle a mix of valid and invalid JSON strings`() {
        val input = listOf(
            """[{"key1":"value1"}]""",
            """[{"key2":null}]""",
            """invalid json""",
            """[{"key3":"value3"}]"""
        )
        val expectedOutput = """
        |[
        |  {"key1":"value1"},
        |  {"key3":"value3"}
        |]
        """.trimMargin()
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should return empty JSON array when all inputs are invalid or contain only null objects`() {
        val input = listOf(
            """[{"key1":null}]""",
            """invalid json""",
            """[]"""
        )
        val expectedOutput = """[]"""
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should handle empty JSON arrays in the input list`() {
        val input = listOf(
            """[]""",
            """[{"key1":"value1"}]""",
            """[]"""
        )
        val expectedOutput = """
        |[
        |  {"key1":"value1"}
        |]
        """.trimMargin()
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should handle whitespace padded JSON strings`() {
        val input = listOf(
            """[ {"key1" : "value1" } ]"""
        )
        val expectedOutput = """[ {"key1" : "value1" } ]"""

        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun `mergeJsonListResults should ignore empty or blank strings in the input list`() {
        val input = listOf(
            """[{"key1":"value1"}]""",
            """ """,
            """[{"key2":"value2"}]""",
            """   """
        )
        val expectedOutput = """
        |[
        |  {"key1":"value1"},
        |  {"key2":"value2"}
        |]
        """.trimMargin()
        val result = mergeJsonListResults(input)
        assertEquals(expectedOutput, result)
    }

    @Test
    fun shouldReturnTrueWhenAllJsonValuesAreNull() {
        val json = """
            	{
            		"TICKER_SYMBOL": null,
            		"LOTT_CAT": null,
            		"DIGIT_LAST_NUMS": null,
            		"LOTT_NO": null
            	}
        """.trimIndent()

        val got = isAllJsonValueNull(json)

        expectThat(got).isEqualTo(true)
    }

    @Test
    fun `isAllJsonValueNull should return false when some values are not null`() {
        val json = """
            {
                "TICKER_SYMBOL": "002727",
                "EXCHANGE_CD": null,
                "NEW_PUB_DATE": "2020-04-15 00:00:00",
                "END_DATE": null
            }
        """.trimIndent()

        val result = isAllJsonValueNull(json)

        expectThat(result).isEqualTo(false)
    }
}
