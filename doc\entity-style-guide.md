# 实体类编写规范 (Entity Class Style Guide)

## 基本结构

1. **包位置**：
   - 所有实体类放在`com.datayes.model`包下

2. **类定义**：
   ```kotlin
   @Entity(name = "表名")
   data class 类名(
       // 字段定义
   ) : PanacheEntityBase {
       companion object : PanacheCompanionBase<类名, Long>
   }
   ```

## 字段规范

1. **主键**：
   ```kotlin
   @Id
   @GeneratedValue(strategy = GenerationType.IDENTITY)
   var id: Long? = null
   ```

2. **普通字段**：
   ```kotlin
   @Column(name = "数据库列名")
   var 字段名: 类型? = 默认值
   ```

3. **特殊类型**：
   - JSON类型：`@Column(name = "列名", columnDefinition = "JSON")`
   - 时间类型：使用`LocalDateTime`

## 命名规范

1. **数据库相关**：
   - 表名：小写下划线（如`llm_responses`）
   - 列名：大写下划线（如`SYS_DATA_ANNO_ID`）

2. **Kotlin相关**：
   - 类名：大驼峰
   - 字段名：小驼峰

## 其他规范

1. 添加Kdoc注释说明实体类用途
2. 时间字段默认值为`LocalDateTime.now()`
3. 使用可空类型(`Type?`)以适应数据库NULL值
4. 使用Quarkus Panache的Kotlin扩展

## 示例模板

```kotlin
package com.datayes.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanionBase
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * 类说明 (Class Description)
 */
@Entity(name = "table_name")
data class EntityName(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "COLUMN_NAME")
    var fieldName: String? = null,

    // 其他字段...

    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime? = LocalDateTime.now(),

    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime? = LocalDateTime.now(),

) : PanacheEntityBase {
    companion object : PanacheCompanionBase<EntityName, Long>
}