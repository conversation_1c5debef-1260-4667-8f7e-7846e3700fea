package com.datayes.util

import jakarta.ws.rs.core.Response
import jakarta.ws.rs.ext.ExceptionMapper
import jakarta.ws.rs.ext.Provider
import java.io.PrintWriter
import java.io.StringWriter

@Provider // 标记这个类为一个 JAX-RS Provider
class GlobalExceptionHandler : ExceptionMapper<Throwable> {

    override fun toResponse(exception: Throwable): Response {
        // 创建一个简单的错误响应对象
        val errorResponse = ErrorResponse(
            message = "25d31251 | ${exception.message}",
            details = getStackTrace(exception) // 可选：添加堆栈跟踪
        )

        // 返回一个包含错误信息的 500 Internal Server Error 响应
        return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
            .entity(errorResponse)
            .type("application/json") // 确保响应类型是 JSON
            .build()
    }

    // 辅助方法，用于获取堆栈跟踪的字符串表示 (可选)
    private fun getStackTrace(throwable: Throwable): String {
        val sw = StringWriter()
        val pw = PrintWriter(sw, true)
        throwable.printStackTrace(pw)
        return sw.buffer.toString()
    }

    // 用于封装错误信息的简单数据类
    data class ErrorResponse(
        var message: String? = null,
        var details: String? = null // 可选
    )
}