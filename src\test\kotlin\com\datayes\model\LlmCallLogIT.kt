package com.datayes.model

import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assumptions.assumeTrue
import strikt.api.expectCatching
import strikt.assertions.isNull
import strikt.assertions.isSuccess
import strikt.assertions.isNotNull

@QuarkusTest
class LlmCallLogIT {

    @Test
    fun `test findById should not throw exception and return null when id does not exist`() {
        // --- Act & Assert ---
        expectCatching { LlmCallLog.findById(-1L) }
            .isSuccess() // 断言没有抛出异常
            .isNull()    // 断言返回值为 null
    }

    @Test
    fun `test findById with max id should return existing record`() {
        // --- Arrange ---
        // 使用原生SQL查询最大的ID，更高效
        val maxId = LlmCallLog.getEntityManager()
            .createQuery("SELECT MAX(id) FROM llm_call_log", Long::class.java)
            .singleResult ?: 0L

        // 假设表中至少有一条记录
        assumeTrue(maxId > 0) { "表中没有记录，跳过测试" }

        // --- Act & Assert ---
        expectCatching { LlmCallLog.findById(maxId) }
            .isSuccess() // 断言没有抛出异常
            .isNotNull() // 断言返回的记录不为null
    }
}