package com.datayes.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanionBase
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id

@Entity(name = "extraction_task")
data class ExtractionTask(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    // EXT_TYPE表示抽取类别为【0-文本，1-表格，2-图片】
    @Column(name = "EXT_TYPE")
    var extType: String? = null,

    // EXT_DESC 数据名称以及简单的规则
    @Column(name = "EXT_DESC")
    var extDesc: String? = null,

    // IS_LIST boolean 是否抽取多条数据
    @Column(name = "IS_LIST")
    var isList: Boolean? = null,

    @Column(name = "QA_ACTIVE_FLG")
    var qaActiveFlg: Int? = null

) : PanacheEntityBase {

    companion object : PanacheCompanionBase<ExtractionTask, Long> {

        fun isTableList(id: Long): Boolean {
            val extractionTask = findById(id) ?: error("未找到相关配置数据")
            return extractionTask.extType?.trim() == "1"
        }
    }

    fun isTable(): Boolean {
        return this.extType?.trim() == "1"
    }
}
