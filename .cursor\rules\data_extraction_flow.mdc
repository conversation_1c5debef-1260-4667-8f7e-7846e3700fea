---
description:
globs:
alwaysApply: false
---
# PDF智能抽取系统数据提取流程 (PDF Intelligent Extraction Flow)

本系统的核心功能是从 PDF 文档中智能提取结构化数据。以下是主要的数据提取流程：

## 数据提取流程 (Data Extraction Flow)

PDF数据提取主要分为以下几个步骤：

1. **读取配置** (Configuration Reading)
   - 从数据库读取表和字段配置信息
   - 判断提取目标是表格还是普通文本
   - 获取是否为列表类型数据

2. **提取内容** (Content Extraction)
   - 针对表格：使用表格提取服务获取结构化表格内容
   - 针对文本：使用文本提取服务获取原始文本

3. **构建 LLM 提示词** (LLM Prompt Building)
   - 根据字段配置和提取规则构建 LLM 提示词
   - 根据内容长度决定是否分片处理

4. **LLM 调用** (LLM Invocation)
   - 发送提示词到 DoubaoLLM 服务
   - 接收并解析 LLM 返回的结构化数据

5. **结果保存** (Result Saving)
   - 将提取结果保存到数据库中的 LlmResponse 表

## 主要 API 端点 (Main API Endpoints)

系统提供以下主要 API 端点：

- [/api/pdf/extract-table-data](mdc:src/main/kotlin/com/datayes/quarkus/rest/PdfResource.kt) - 根据表ID提取单条数据
- [/api/pdf/extract-all-data](mdc:src/main/kotlin/com/datayes/quarkus/rest/PdfResource.kt) - 批量提取所有数据
- [/api/pdf/extract-unprocessed-data](mdc:src/main/kotlin/com/datayes/quarkus/rest/PdfResource.kt) - 批量提取未处理数据

## 数据处理特性 (Data Processing Features)

1. **内容分片处理** (Content Chunking)
   - 对于超长内容，系统会自动分片处理，避免超出 LLM 上下文限制
   - 使用 `splitPdfContent` 方法将内容分成多个片段

2. **批量并行处理** (Batch Parallel Processing)
   - 批量提取时使用线程池并行处理多个文档
   - 使用 `CountDownLatch` 等待所有任务完成

3. **结果合并** (Result Merging)
   - 对于列表类型数据，系统会将多个分片结果合并成一个完整列表
   - 使用 `combineListResults` 方法合并多个 JSON 数组
