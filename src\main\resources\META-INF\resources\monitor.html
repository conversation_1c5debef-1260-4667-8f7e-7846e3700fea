<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JVM Monitor</title>
    <!-- Add HTMX library -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
    <style>
        body { 
            font-family: sans-serif; 
            line-height: 1.6; 
            padding: 20px; 
            margin: 0;
            background-color: #1e1e1e;
            color: #f0f0f0;
            background-image: 
                radial-gradient(circle at 25px 25px, rgba(123, 247, 153, 0.1) 2%, transparent 0%),
                radial-gradient(circle at 75px 75px, rgba(144, 202, 249, 0.1) 2%, transparent 0%);
            background-size: 100px 100px;
        }
        .container { 
            width: 100%; 
            padding: 0 15px;
        }
        h1, h2 { 
            border-bottom: 1px solid #444; 
            padding-bottom: 5px; 
            color: #7bf799;
            position: relative;
            display: inline-block;
        }
        h1::after, h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #7bf799, #90caf9);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
        .status-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); 
            gap: 15px; 
            margin-bottom: 20px; 
        }
        .status-item { 
            background-color: #2a2a2a; 
            border: 1px solid #444; 
            padding: 15px; 
            border-radius: 8px; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .status-item:hover {
            transform: translateY(-5px) scale(1.02);
            background-color: #333;
            z-index: 10;
            box-shadow: 0 10px 20px rgba(0,0,0,0.3);
        }
        .status-item::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(144, 202, 249, 0.2) 0%, transparent 60%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }
        .status-item:hover::before {
            opacity: 1;
            animation: pulse-radial 2s infinite;
        }
        @keyframes pulse-radial {
            0% { transform: scale(0.8); opacity: 0.3; }
            50% { transform: scale(1); opacity: 0.5; }
            100% { transform: scale(0.8); opacity: 0.3; }
        }
        .status-item strong { 
            display: block; 
            margin-bottom: 5px; 
            color: #90caf9; 
            font-size: 1.1em;
        }
        .memory-gauge {
            height: 10px;
            width: 100%;
            background-color: #333;
            border-radius: 10px;
            margin-top: 10px;
            overflow: hidden;
            position: relative;
        }
        .memory-used {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #ffeb3b, #f44336);
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        button { 
            padding: 10px 15px; 
            margin-right: 10px; 
            margin-bottom: 10px;
            cursor: pointer; 
            border: none; 
            border-radius: 4px; 
            background-color: #007bff; 
            color: white; 
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: translateX(-100%);
        }
        button:hover::after {
            animation: shimmer 1.5s infinite;
        }
        @keyframes shimmer {
            100% { transform: translateX(100%); }
        }
        button:hover { 
            background-color: #0056b3; 
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        #stacktrace-container { margin-top: 20px; }
        #stacktrace-pre { 
            background-color: #2a2a2a; 
            color: #ddd;
            padding: 15px; 
            border-radius: 8px; 
            max-height: 500px; 
            overflow-y: auto; 
            white-space: pre-wrap; 
            word-wrap: break-word; 
            box-shadow: inset 0 0 10px rgba(0,0,0,0.3);
            position: relative;
        }
        #stacktrace-pre::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 10px;
            background: linear-gradient(to right, transparent, #2a2a2a);
            z-index: 10;
        }
        .hidden { display: none; }
        
        /* Styles for logs section */
        #logs-container { 
            margin-top: 20px; 
            width: 100%;
        }
        #logs-content { 
            background-color: #000; 
            color: #ddd; 
            padding: 15px; 
            border-radius: 8px; 
            max-height: 600px; 
            overflow-y: auto; 
            font-family: monospace;
            font-size: 0.9em;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.5);
            position: relative;
        }
        .log-entry { 
            margin: 2px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            transition: all 0.2s ease;
            padding: 2px 5px;
            border-radius: 3px;
            position: relative;
            animation: fadeIn 0.3s ease;
        }
        .log-entry.new-entry {
            animation: highlight 2s ease;
        }
        @keyframes highlight {
            0% { background-color: rgba(144, 202, 249, 0.3); }
            100% { background-color: transparent; }
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .log-entry:hover {
            background-color: #2a2a2a;
            transform: translateX(5px);
        }
        /* Log level styling */
        .log-error { color: #ff5252; }
        .log-warn { color: #ffab40; }
        .log-info { color: #7bf799; }
        .log-debug { color: #b0bec5; }
        .log-trace { color: #90caf9; }
        .log-controls {
            margin-bottom: 15px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
        }
        .log-controls select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #444;
            background-color: #2a2a2a;
            color: #f0f0f0;
            transition: all 0.2s ease;
        }
        .log-controls select:hover {
            border-color: #90caf9;
            box-shadow: 0 0 0 2px rgba(144, 202, 249, 0.3);
        }
        .log-count {
            font-weight: bold;
            color: #ddd;
            background-color: #444;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            min-width: 60px;
            text-align: center;
            animation: pulse 2s infinite;
        }
        
        /* Theme toggle */
        .theme-switch {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .theme-switch label {
            display: inline-block;
            width: 50px;
            height: 24px;
            position: relative;
            cursor: pointer;
        }
        .theme-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .theme-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #444;
            transition: .4s;
            border-radius: 24px;
        }
        .theme-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .theme-slider {
            background-color: #7bf799;
        }
        input:checked + .theme-slider:before {
            transform: translateX(26px);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .status-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
            .log-controls {
                flex-direction: column;
                align-items: flex-start;
            }
            .log-controls > * {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="theme-switch">
        <span>🌙</span>
        <label>
            <input type="checkbox" id="theme-toggle">
            <span class="theme-slider"></span>
        </label>
        <span>☀️</span>
    </div>
    
    <div class="container">
        <h1>JVM Monitor</h1>

        <h2>JVM Status</h2>
        <div id="jvm-status" class="status-grid">
            <div class="status-item"><strong>Loading...</strong></div>
        </div>

        <h2>Actions</h2>
        <button id="show-stacktrace-btn">Show JVM Stack Trace</button>
        <button id="copy-stacktrace-btn" class="hidden">Copy Stack Trace</button>
        <button id="refresh-jvm-btn">Refresh JVM Status</button>

        <div id="stacktrace-container" class="hidden">
            <h2>JVM Stack Trace</h2>
            <pre id="stacktrace-pre">Loading stack trace...</pre>
        </div>
        
        <!-- Add Logs Section -->
        <h2>Application Logs</h2>
        <div id="logs-container">
            <div class="log-controls">
                <button id="refresh-logs-btn" 
                        hx-get="/api/logs/html" 
                        hx-target="#logs-content" 
                        hx-trigger="click">
                    Refresh Logs
                </button>
                <select id="log-limit" hx-get="/api/logs/html" hx-target="#logs-content" hx-trigger="change">
                    <option value="20">Last 20</option>
                    <option value="50">Last 50</option>
                    <option value="100" selected>Last 100</option>
                    <option value="200">Last 200</option>
                    <option value="500">Last 500</option>
                </select>
                <select id="log-filter" class="log-filter">
                    <option value="all" selected>All Levels</option>
                    <option value="error">ERROR only</option>
                    <option value="warn">WARN+</option>
                    <option value="info">INFO+</option>
                    <option value="debug">DEBUG+</option>
                    <option value="trace">TRACE+</option>
                </select>
                <button id="auto-refresh-logs-btn">Auto Refresh: OFF</button>
                <span id="log-count" class="log-count">0 logs</span>
            </div>
            <div id="logs-content" 
                 hx-get="/api/logs/html" 
                 hx-trigger="load"
                 hx-swap="innerHTML">
                Loading logs...
            </div>
        </div>
    </div>

    <script>
        const jvmStatusDiv = document.getElementById('jvm-status');
        const showStackTraceBtn = document.getElementById('show-stacktrace-btn');
        const copyStackTraceBtn = document.getElementById('copy-stacktrace-btn');
        const stackTraceContainer = document.getElementById('stacktrace-container');
        const stackTracePre = document.getElementById('stacktrace-pre');
        const autoRefreshLogsBtn = document.getElementById('auto-refresh-logs-btn');
        const logLimitSelect = document.getElementById('log-limit');
        const refreshJvmBtn = document.getElementById('refresh-jvm-btn');
        const themeToggle = document.getElementById('theme-toggle');
        
        // Theme switcher
        themeToggle.addEventListener('change', function() {
            if (this.checked) {
                document.body.style.backgroundColor = '#f0f0f0';
                document.body.style.color = '#333';
                document.documentElement.style.setProperty('--status-bg', '#fff');
                document.documentElement.style.setProperty('--border-color', '#ddd');
            } else {
                document.body.style.backgroundColor = '#1e1e1e';
                document.body.style.color = '#f0f0f0';
                document.documentElement.style.setProperty('--status-bg', '#2a2a2a');
                document.documentElement.style.setProperty('--border-color', '#444');
            }
        });

        function formatBytes(bytes, decimals = 2) {
             if (bytes === 0) return '0 Bytes';
             const k = 1024;
             const dm = decimals < 0 ? 0 : decimals;
             const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
             const i = Math.floor(Math.log(bytes) / Math.log(k));
             return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }

        // Add random animation to status items for eye candy
        function addRandomAnimations() {
            const statusItems = document.querySelectorAll('.status-item');
            statusItems.forEach(item => {
                // Random delay for staggered animation
                const delay = Math.random() * 0.5;
                item.style.animationDelay = `${delay}s`;
                
                // Random subtle rotation 
                const rotateAngle = (Math.random() * 2 - 1) * 0.5;
                item.style.transform = `rotate(${rotateAngle}deg)`;
                
                // Reset on hover
                item.addEventListener('mouseenter', () => {
                    item.style.transform = 'translateY(-5px) scale(1.02)';
                });
                item.addEventListener('mouseleave', () => {
                    item.style.transform = `rotate(${rotateAngle}deg)`;
                });
            });
        }
        
        async function fetchJvmStatus() {
            try {
                const response = await fetch('/monitor/jvm/status');
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                
                // Calculate memory usage percentage
                const usedPercentage = (data.usedMemoryMb / data.maxMemoryMb) * 100;
                const committedPercentage = (data.totalMemoryMb / data.maxMemoryMb) * 100;
                
                jvmStatusDiv.innerHTML = `
                    <div class="status-item">
                        <strong>Uptime:</strong> ${data.uptime}
                    </div>
                    <div class="status-item">
                        <strong>Memory Usage:</strong> ${data.usedMemoryMb} MB / ${data.maxMemoryMb} MB
                        <div class="memory-gauge">
                            <div class="memory-used" style="width: ${usedPercentage}%"></div>
                        </div>
                    </div>
                    <div class="status-item">
                        <strong>Committed Heap:</strong> ${data.totalMemoryMb} MB
                        <div class="memory-gauge">
                            <div class="memory-used" style="width: ${committedPercentage}%; background: linear-gradient(90deg, #4caf50, #4caf50);"></div>
                        </div>
                    </div>
                    <div class="status-item"><strong>Max Heap:</strong> ${data.maxMemoryMb} MB</div>
                    <div class="status-item"><strong>Processors:</strong> ${data.availableProcessors}</div>
                    <div class="status-item"><strong>Live Threads:</strong> ${data.threadCount}</div>
                    <div class="status-item"><strong>Peak Threads:</strong> ${data.peakThreadCount}</div>
                    <div class="status-item"><strong>Total Started Threads:</strong> ${data.totalStartedThreadCount}</div>
                `;
                
                // Add flare to the UI
                addRandomAnimations();
                
                // Change memory gauge color based on usage
                const memoryGauge = document.querySelector('.memory-used');
                if (usedPercentage > 80) {
                    memoryGauge.style.backgroundColor = '#f44336'; // Red for high usage
                } else if (usedPercentage > 50) {
                    memoryGauge.style.backgroundColor = '#ffeb3b'; // Yellow for medium usage
                } else {
                    memoryGauge.style.backgroundColor = '#4caf50'; // Green for low usage
                }
                
            } catch (error) {
                jvmStatusDiv.innerHTML = `<div class="status-item"><strong>Error loading JVM status:</strong> ${error.message}</div>`;
                console.error('Error fetching JVM status:', error);
            }
        }

        async function fetchAndShowStackTrace() {
            stackTraceContainer.classList.remove('hidden');
            stackTracePre.textContent = 'Loading stack trace...';
            try {
                const response = await fetch('/monitor/jvm/stacktrace');
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const stackTraceText = await response.text();
                stackTracePre.textContent = stackTraceText;
                copyStackTraceBtn.classList.remove('hidden');
                
                // Highlight some keywords in the stack trace for better visibility
                highlightKeywords();
            } catch (error) {
                stackTracePre.textContent = `Error loading stack trace: ${error.message}`;
                console.error('Error fetching stack trace:', error);
                copyStackTraceBtn.classList.add('hidden');
            }
        }
        
        function highlightKeywords() {
            const text = stackTracePre.textContent;
            const keywordMap = {
                'Exception': '<span style="color:#ff5252">Exception</span>',
                'Error': '<span style="color:#ff5252">Error</span>',
                'Caused by': '<span style="color:#ffab40">Caused by</span>',
                'at com.datayes': '<span style="color:#7bf799">at com.datayes</span>',
                'java.lang': '<span style="color:#90caf9">java.lang</span>',
            };
            
            let highlightedText = text;
            Object.keys(keywordMap).forEach(keyword => {
                highlightedText = highlightedText.replace(
                    new RegExp(keyword, 'g'), 
                    keywordMap[keyword]
                );
            });
            
            stackTracePre.innerHTML = highlightedText;
        }

        function copyStackTraceToClipboard() {
            if (navigator.clipboard && window.isSecureContext) {
                // Get plain text content (not the HTML with highlights)
                const plainText = stackTracePre.textContent;
                
                navigator.clipboard.writeText(plainText)
                    .then(() => {
                        const originalText = copyStackTraceBtn.textContent;
                        copyStackTraceBtn.textContent = '✓ Copied!';
                        copyStackTraceBtn.style.backgroundColor = '#4caf50';
                        
                        setTimeout(() => {
                            copyStackTraceBtn.textContent = originalText;
                            copyStackTraceBtn.style.backgroundColor = '#007bff';
                        }, 2000);
                    })
                    .catch(err => {
                        alert('Failed to copy stack trace.');
                        console.error('Failed to copy text: ', err);
                    });
            } else {
                // Fallback for older browsers or insecure contexts (though unlikely for localhost)
                 alert('Clipboard API not available. Please copy manually.');
            }
        }
        
        // Auto-refresh logs functionality
        let logsInterval;
        function toggleAutoRefreshLogs() {
            if (logsInterval) {
                clearInterval(logsInterval);
                logsInterval = null;
                autoRefreshLogsBtn.textContent = 'Auto Refresh: OFF';
                autoRefreshLogsBtn.style.backgroundColor = '#007bff';
            } else {
                logsInterval = setInterval(() => {
                    const limit = logLimitSelect.value;
                    htmx.ajax('GET', `/api/logs/html?limit=${limit}`, {target: '#logs-content'});
                }, 5000); // Refresh every 5 seconds
                autoRefreshLogsBtn.textContent = 'Auto Refresh: ON';
                autoRefreshLogsBtn.style.backgroundColor = '#28a745';
            }
        }
        
        // Log filtering functionality
        const logFilterSelect = document.getElementById('log-filter');
        const logCountSpan = document.getElementById('log-count');
        
        function filterLogs() {
            const filter = logFilterSelect.value;
            const logEntries = document.querySelectorAll('.log-entry');
            let visibleCount = 0;
            
            logEntries.forEach(entry => {
                switch(filter) {
                    case 'error':
                        entry.style.display = entry.classList.contains('log-error') ? '' : 'none';
                        break;
                    case 'warn':
                        entry.style.display = (entry.classList.contains('log-error') || 
                                            entry.classList.contains('log-warn')) ? '' : 'none';
                        break;
                    case 'info':
                        entry.style.display = (!entry.classList.contains('log-debug') && 
                                            !entry.classList.contains('log-trace')) || 
                                            entry.classList.contains('log-error') || 
                                            entry.classList.contains('log-warn') || 
                                            entry.classList.contains('log-info') ? '' : 'none';
                        break;
                    case 'debug':
                        entry.style.display = !entry.classList.contains('log-trace') || 
                                            entry.classList.contains('log-debug') ? '' : 'none';
                        break;
                    default:
                        entry.style.display = ''; // Show all
                }
                
                if (entry.style.display !== 'none') {
                    visibleCount++;
                }
            });
            
            // Update log count
            logCountSpan.textContent = `${visibleCount} log${visibleCount !== 1 ? 's' : ''}`;
        }
        
        // Track current log entries to detect new ones
        let currentLogEntries = new Set();
        
        // Function to highlight new log entries
        function highlightNewEntries() {
            const logEntries = document.querySelectorAll('.log-entry');
            const newEntryIds = new Set();
            
            logEntries.forEach(entry => {
                const entryId = entry.textContent.trim();
                newEntryIds.add(entryId);
                
                if (!currentLogEntries.has(entryId)) {
                    entry.classList.add('new-entry');
                    setTimeout(() => {
                        entry.classList.remove('new-entry');
                    }, 3000);
                }
            });
            
            currentLogEntries = newEntryIds;
        }
        
        // Event listener for log filtering
        logFilterSelect.addEventListener('change', filterLogs);
        
        // After logs are loaded with htmx, update the count and apply filters
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            if (evt.detail.target.id === 'logs-content') {
                filterLogs();
                highlightNewEntries();
            }
        });

        // Event Listeners
        showStackTraceBtn.addEventListener('click', fetchAndShowStackTrace);
        copyStackTraceBtn.addEventListener('click', copyStackTraceToClipboard);
        autoRefreshLogsBtn.addEventListener('click', toggleAutoRefreshLogs);
        refreshJvmBtn.addEventListener('click', fetchJvmStatus);
        
        // Update the URL when log limit changes
        logLimitSelect.addEventListener('change', function() {
            const logContent = document.getElementById('logs-content');
            logContent.setAttribute('hx-get', `/api/logs/html?limit=${this.value}`);
        });

        // Initial load
        fetchJvmStatus();
        
        // Auto-refresh JVM status every 10 seconds
        setInterval(fetchJvmStatus, 10000);
    </script>
</body>
</html> 