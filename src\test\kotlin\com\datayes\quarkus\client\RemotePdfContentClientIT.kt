package com.datayes.quarkus.client

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.contains

/**
 * (Integration Test) for (RemotePdfContentClient)
 * 直接调用实际(PDF内容提取服务)，验证接口和数据流。
 * 注意：需保证 pdf-content-api 指向可访问的服务。
 */
@QuarkusTest
class RemotePdfContentClientIT {

    @Inject
    @RestClient
    lateinit var client: RemotePdfContentClient

    @Test
    fun `should extract content from remote pdf`() {
        val pdfUrl = "https://bigdata-s3.datayes.com/pipeline/report/2017-08-04/20170804_SZ300444_27642381.pdf"

        val request = ExtractContentRequest(pdfUrl = pdfUrl)

        val result = client.extractContent(request)

        // combine pdf content
        val combinedContent = result.take(5).joinToString(separator = "\n")

        expectThat(combinedContent).contains("证券代码")
        expectThat(combinedContent).contains("300444")
    }
}
