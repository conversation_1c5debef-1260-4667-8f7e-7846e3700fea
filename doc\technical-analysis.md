# PDF智能抽取系统技术方案（基于 Kotlin/Quarkus/MySQL，极简数据驱动风格）

---

## 一、架构与分层 (Architecture & Layering)

- **核心逻辑 (Core Logic)：**  
  纯 Kotlin 实现，不依赖任何框架 (framework)，只用 Kotlin 标准库 (Kotlin Standard Library)。所有核心算法、数据处理、业务流程均在此层实现，直接放在 com.datayes 包下。
- **框架集成层 (Framework Integration Layer)：**  
  所有与 Quarkus、REST API、数据库 (MySQL) 相关的代码，全部集中在 com.datayes.quarkus 包下，核心逻辑绝不涉及框架内容。
- **数据结构 (Data Structure)：**  
  统一用 Kotlin data class 定义，数据库实体采用 JPA 注解 (JPA Annotation) 风格，字段支持 var、默认值和可空类型。
- **命名空间 (Namespace)：**  
  所有函数集合用 Kotlin object 组织，模拟静态方法 (static function)，避免 class unless 必须。
- **领域模型 (Domain Model)：**  
  允许数据库实体 (entity) 直接作为领域模型 (domain model)，如需 DTO，单独定义并提供转换函数。
- **面向数据 (Data-Oriented)：**  
  极少使用 OOP，除非有充分理由（如多态、复杂状态机等），否则一律数据驱动。

---

## 二、项目结构 (Project Structure)

```
pdf-extraction-system/
├── src/
│   ├── main/
│   │   ├── kotlin/
│   │   │   └── com/
│   │   │       └── datayes/
│   │   │           ├── model/             # data class，数据库实体/领域模型
│   │   │           ├── extraction/        # object 组织的核心算法与流程
│   │   │           ├── util/              # 工具 object
│   │   │           └── quarkus/           # 仅存放 Quarkus 相关代码
│   │   │               ├── resource/      # XXXResource，REST API
│   │   │               ├── repository/    # Quarkus ORM/Panache
│   │   │               └── config/        # Quarkus 配置
│   │   ├── resources/
│   │   │   ├── application.properties
│   ├── test/
│   │   └── kotlin/
│   │       └── com/
│   │           └── datayes/
│   │               ├── extraction/        # 纯 Kotlin 测试，无 mock
│   │               └── util/
├── pom.xml
└── README.md
```

---

## 三、数据结构定义 (Data Structure Definition)

### 1. 数据源配置表实体 (SysDataAnno Entity)

```kotlin
// com/datayes/model/SysDataAnno.kt
package com.datayes.model

import io.quarkus.hibernate.orm.panache.PanacheEntityBase
import java.time.LocalDateTime
import javax.persistence.*

@Entity(name = "sys_data_anno")
data class SysDataAnno(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "TABLE_ID")
    var tableId: Long? = null,

    @Column(name = "RELATION_FIELDS")
    var relationFields: String? = null,

    @Column(name = "ANNO_ID")
    var annoId: String? = null,

    @Column(name = "DB")
    var db: String? = null,

    @Column(name = "REPORT_ADDRESS")
    var reportAddress: String? = null,

    @Column(name = "CON_DATA")
    var conData: String? = null,

    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime? = LocalDateTime.now(),

    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime? = LocalDateTime.now()
) : PanacheEntityBase()
```

### 2. 字段配置表实体 (SysColumnType Entity)

```kotlin
// com/datayes/model/SysColumnType.kt
package com.datayes.model

import io.quarkus.hibernate.orm.panache.PanacheEntityBase
import java.time.LocalDateTime
import javax.persistence.*

@Entity(name = "sys_column_type")
data class SysColumnType(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "TABLE_ID")
    var tableId: Long? = null,

    @Column(name = "COLUMN_ID")
    var columnId: Long? = null,

    @Column(name = "IS_EXTRACTED")
    var isExtracted: Boolean? = null,

    @Column(name = "COLUMN_TYPE")
    var columnType: Int? = null,

    @Column(name = "TASK_ID")
    var taskId: Long? = null,

    @Column(name = "COLUMN_NAME")
    var columnName: String? = null,

    @Column(name = "COLUMN_NAME_EN")
    var columnNameEn: String? = null,

    @Column(name = "DEC")
    var dec: String? = null,

    @Column(name = "RULE")
    var rule: String? = null,

    @Column(name = "TABLE_NAME")
    var tableName: String? = null,

    @Column(name = "EXT_TYPE")
    var extType: Int? = null,

    @Column(name = "IS_KEY")
    var isKey: Boolean? = null,

    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime? = LocalDateTime.now(),

    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime? = LocalDateTime.now()
) : PanacheEntityBase()
```

### 3. 抽取任务元数据表实体 (ExtractionResult Entity)

```kotlin
// com/datayes/model/ExtractionResult.kt
package com.datayes.model

import io.quarkus.hibernate.orm.panache.PanacheEntityBase
import java.time.LocalDateTime
import javax.persistence.*

@Entity(name = "extraction_result")
data class ExtractionResult(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "TABLE_ID")
    var tableId: Long? = null,

    @Column(name = "PDF_URL")
    var pdfUrl: String? = null,

    @Column(name = "EXTRACTED_JSON")
    var extractedJson: String? = null,

    @Column(name = "RAW_TEXT")
    var rawText: String? = null,

    @Column(name = "LLM_PROMPT")
    var llmPrompt: String? = null,

    @Column(name = "STATUS")
    var status: Int? = null,

    @Column(name = "ERROR_MSG")
    var errorMsg: String? = null,

    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime? = LocalDateTime.now(),

    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime? = LocalDateTime.now()
) : PanacheEntityBase()
```

### 4. 抽取明细表实体 (ExtractionItem Entity)

```kotlin
// com/datayes/model/ExtractionItem.kt
package com.datayes.model

import io.quarkus.hibernate.orm.panache.PanacheEntityBase
import java.time.LocalDateTime
import javax.persistence.*

@Entity(name = "extraction_item")
data class ExtractionItem(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "RESULT_ID")
    var resultId: Long? = null,

    // 以下字段根据 LLM 返回的 Kotlin data class 动态定义
    @Column(name = "FIELD1")
    var field1: String? = null,

    @Column(name = "FIELD2")
    var field2: String? = null,

    // ... 其他字段

    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime? = LocalDateTime.now(),

    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime? = LocalDateTime.now()
) : PanacheEntityBase()
```

---

## 四、核心逻辑组织 (Core Logic Organization)

- 以 object 作为命名空间，所有函数为顶级函数 (top-level function) 或 object 内部静态函数。
- 示例：

```kotlin
// com/datayes/extraction/PdfExtraction.kt
package com.datayes.llm

object PdfExtraction {
  fun extractTextFromPdfUrl(pdfUrl: String): String {
    // 纯 Kotlin 实现或调用外部 API
    // ...
    return ""
  }
}

// com/datayes/extraction/LlmPromptBuilder.kt
package com.datayes.llm

object LlmPromptBuilder {
  fun buildPrompt(
    pdfText: String,
    config: List<com.datayes.model.SysColumnType>,
    dataClassSource: String
  ): String {
    // 纯 Kotlin 拼接 prompt
    // ...
    return ""
  }
}

// com/datayes/extraction/LlmResponseParser.kt
package com.datayes.llm

object LlmResponseParser {
  fun parseResultJson(resultXml: String): List<com.datayes.model.ExtractionItem> {
    // 解析 <result> 标签，反序列化为 ExtractionItem 列表
    // ...
    return emptyList()
  }
}
```

---

## 五、框架集成 (Framework Integration)

- com.datayes.quarkus.rest/ 目录下定义 REST API，所有 XXXResource 只负责参数接收、调用 com.datayes 层逻辑、返回结果。
- 示例：

```kotlin
// com/datayes/quarkus/resource/PdfResource.kt
package com.datayes.quarkus.rest

import com.datayes.llm.PdfExtraction
import javax.ws.rs.*
import javax.ws.rs.core.MediaType
import javax.ws.rs.core.Response

@Path("/api/pdf")
object PdfResource {
  @POST
  @Path("/extract-text")
  @Produces(MediaType.TEXT_PLAIN)
  fun extractText(pdfUrl: String): Response {
    val text = PdfExtraction.extractTextFromPdfUrl(pdfUrl)
    return Response.ok(text).build()
  }
}
```

- com.datayes.quarkus.repository/ 目录下定义数据库操作，采用 Quarkus ORM (Panache)。
- 仅此处使用 ORM 注解，com.datayes 层不涉及任何 ORM/框架内容。

---

## 六、数据库表结构 (Database Table Structure)

### 1. extraction_result (抽取任务元数据表 Extraction Task Meta Table)

| 字段名 (Field)   | 类型 (Type)   | 说明 (Description)                          |
|------------------|--------------|---------------------------------------------|
| ID               | bigint(20)   | 主键，自增 (Primary Key, auto-increment)    |
| TABLE_ID         | bigint(20)   | 关联 sys_data_anno.TABLE_ID (Foreign key)   |
| PDF_URL          | varchar(500) | 原始PDF文件的URL (Original PDF URL)         |
| EXTRACTED_JSON   | text         | 提取结果的JSON字符串 (Extracted JSON string)|
| RAW_TEXT         | longtext     | PDF原文文本 (Raw text from PDF)             |
| LLM_PROMPT       | longtext     | 发送给大模型的prompt内容 (LLM prompt)       |
| STATUS           | int(11)      | 处理状态，如0-失败，1-成功 (Status)         |
| ERROR_MSG        | varchar(1000)| 错误信息 (Error message, if any)            |
| CREATE_TIME      | datetime     | 创建时间 (Create time)                      |
| UPDATE_TIME      | datetime     | 更新时间 (Update time)                      |

### 2. extraction_item (抽取明细表 Extraction Item Table)

| 字段名 (Field)         | 类型 (Type)     | 说明 (Description)                                   |
|------------------------|-----------------|------------------------------------------------------|
| ID                     | bigint(20)      | 主键，自增 (Primary Key, auto-increment)             |
| RESULT_ID              | bigint(20)      | 关联 extraction_result.ID (Foreign key reference)     |
| FIELD1                 | ...             | Kotlin 数据类的字段1 (Field1 of Kotlin data class)   |
| FIELD2                 | ...             | Kotlin 数据类的字段2 (Field2 of Kotlin data class)   |
| ...                    | ...             | 其他字段 (Other fields as needed)                    |
| CREATE_TIME            | datetime        | 创建时间 (Create time)                               |
| UPDATE_TIME            | datetime        | 更新时间 (Update time)                               |

---

## 七、测试策略 (Testing Strategy)

- 所有核心逻辑均为纯 Kotlin，无依赖、无 mock，直接测试。
- 示例：

```kotlin
// test/kotlin/com/datayes/extraction/PdfExtractionTest.kt
package com.datayes.llm

import kotlin.test.*

class PdfExtractionTest {
  @Test
  fun testExtractTextFromPdfUrl() {
    val result = PdfExtraction.extractTextFromPdfUrl("http://example.com/test.pdf")
    assertTrue(result.isNotEmpty())
  }
}
```

---

## 八、数据驱动与简单优先 (Data-Oriented & Simplicity First)

- 只用 data class、object、顶级函数，极少用 class/OOP。
- 只在极端需要多态、复杂状态机等场景下才用 OOP，且需单独说明理由。
- 所有流程、数据流均以数据结构为核心，函数只做数据变换。

---

## 九、数据库实体即领域模型 (Entity as Domain Model)

- 允许直接用数据库实体作为领域模型，便于维护和扩展。
- 如需 DTO，单独定义并提供简单转换函数。

---

## 十、配置与扩展 (Configuration & Extensibility)

- 所有配置集中在 application.properties。
- LLM 服务、PDF 提取服务等外部依赖均通过配置注入，便于切换和扩展。

---

## 十一、接口设计 (API Design)

- /api/pdf/extract-text：POST，参数为 PDF URL，返回纯文本。
- /api/llm/extract：POST，参数为 prompt，返回 <result> 包裹的 JSON。
- /api/extraction/submit：POST，参数为 PDF URL + 配置，触发完整抽取流程，返回任务状态与结果。
- /api/extraction/result/{id}：GET，查询抽取任务元数据。
- /api/extraction/item/{resultId}：GET，查询某次任务下所有抽取明细。

---

## 十二、基础设施建议 (Infrastructure Recommendation)

- 推荐容器化 (Containerization)，使用 (Docker) 部署 Quarkus 应用。
- 可选用 (Kubernetes) 进行弹性伸缩与服务编排。
- MySQL 高可用部署，建议主从或集群方案，定期备份。
- 日志集中管理，可接入 (ELK Stack) 或 (Prometheus + Grafana) 监控应用健康与性能。
- API 需鉴权 (Authentication) 与授权 (Authorization)，可集成 (JWT) 或 (OAuth2)。
- 外部服务调用需配置超时、重试与熔断 (Circuit Breaker)。

---

## 十三、开发原则总结 (Development Principles Summary)

- 纯 Kotlin 数据驱动开发，核心逻辑与框架解耦，极简、易维护。
- object 组织命名空间，data class + JPA 注解定义数据结构。
- Quarkus 相关代码集中于 com.datayes.quarkus 包，REST API 只做参数转发与结果返回。
- 测试直接针对纯 Kotlin 逻辑，无需 mock。
- 数据库实体可直接作为领域模型，便于维护。
- 始终优先简单、数据驱动、标准库优先。