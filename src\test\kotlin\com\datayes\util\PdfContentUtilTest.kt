package com.datayes.util

import com.datayes.quarkus.service.PdfTextExtractor
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class PdfContentUtilTest {


    @Test
    fun `test empty content list`() {
        val result = PdfTextExtractor.splitPdfContent(emptyList())
        assertTrue(result.isEmpty())
    }

    @Test
    fun `test single line under max size`() {
        val content = listOf("Short content")
        val result = PdfTextExtractor.splitPdfContent(content, 24000)
        assertEquals(1, result.size)
        assertEquals(content, result[0])
    }

    @Test
    fun `test multiple lines under max size`() {
        val content = listOf(
            "Line 1",
            "Line 2",
            "Line 3"
        )
        val result = PdfTextExtractor.splitPdfContent(content, 24000)
        assertEquals(1, result.size)
        assertEquals(content, result[0])
    }

    @Test
    fun `test content split into multiple chunks`() {
        // Create a list of strings where total size exceeds maxChunkSize
        val maxChunkSize = 35
        val content = listOf(
            "This is line one", // 15 chars
            "This is line two", // 15 chars
            "This is line three", // 17 chars
            "This is line four" // 16 chars
        )

        val result = PdfTextExtractor.splitPdfContent(content, maxChunkSize)

        // Should split into multiple chunks
        assertEquals(2, result.size)

        // First chunk should contain "This is line one" and "This is line two"
        assertEquals(2, result[0].size)
        assertEquals("This is line one", result[0][0])
        assertEquals("This is line two", result[0][1])

        // Second chunk should contain "This is line three" and "This is line four"
        assertEquals(2, result[1].size)
        assertEquals("This is line three", result[1][0])
        assertEquals("This is line four", result[1][1])
    }

    @Test
    fun `test single line exceeding max size is kept together`() {
        val longLine = "A".repeat(25000)
        val content = listOf(longLine)

        val result = PdfTextExtractor.splitPdfContent(content, 24000)

        assertEquals(1, result.size)
        assertEquals(content, result[0])
    }

    @Test
    fun `test default max chunk size`() {
        val content = listOf("A".repeat(12000), "B".repeat(12000))
        val result = PdfTextExtractor.splitPdfContent(content) // Using default maxChunkSize = 24000

        assertEquals(1, result.size)
        assertEquals(content, result[0])
    }
}