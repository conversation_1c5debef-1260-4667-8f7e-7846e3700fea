# 项目领域分析

## 领域词汇 (Domain Vocabulary)
- PDF资源类 (PDF Resource)：处理PDF文档数据提取的REST API资源类
- 表数据提取 (Table Data Extraction)：从PDF表格中提取结构化数据的过程
- 豆包大模型 (Doubao LLM)：项目使用的大语言模型，用于从PDF内容中提取结构化数据
- LLM提示构建器 (LLM Prompt Builder)：负责构建用于数据提取的LLM提示的工具
- 系统数据注解 (SysDataAnno)：存储数据提取任务配置的数据实体
- 系统列类型 (SysColumnType)：定义数据表中列的类型和提取规则的数据实体
- 系统表类型 (SysTableType)：定义表格类型和相关属性的数据实体
- 列信息 (ColumnInfo)：包含列名、英文列名和提取规则的数据结构
- 表格数据 (TableData)：包含表格标题、前置内容和表格内容的数据结构
- 令牌使用统计 (Token Usage Stats)：跟踪和记录LLM API调用中令牌使用情况的工具
- 批量处理响应 (Batch Processing Response)：用于返回批量处理任务结果的数据结构
- 结果提取 (Result Extraction)：从LLM响应中提取结构化数据的过程
- 内容分块 (Content Chunking)：将长文本内容分割成较小块以适应LLM处理的技术

## 动作 (Actions)
1. 提取表格数据 (Extract Table Data)：
   - 触发条件：接收到包含dataAnnoId的表数据提取请求
   - 预期效果：从PDF文档中提取结构化数据并返回JSON格式结果

2. 处理PDF表格 (Process PDF Tables)：
   - 触发条件：从PDF服务获取表格数据后
   - 预期效果：为每个表格构建提示并调用LLM，合并所有表格的AI响应

3. 处理文本内容 (Process Text Content)：
   - 触发条件：从PDF服务获取文本内容后
   - 预期效果：根据内容长度可能分块处理，为每块构建提示并调用LLM

4. 发送LLM提示 (Send LLM Prompt)：
   - 触发条件：构建完成提示后
   - 预期效果：向豆包API发送请求并获取响应，同时更新令牌使用统计

5. 构建表格提示 (Generate Table Prompt)：
   - 触发条件：需要从表格数据提取信息时
   - 预期效果：根据表格数据和列信息生成适合LLM处理的提示

6. 构建文本提示 (Generate Text Prompt)：
   - 触发条件：需要从文本内容提取信息时
   - 预期效果：根据文本内容和列信息生成适合LLM处理的提示

7. 批量提取所有数据 (Extract All Data)：
   - 触发条件：调用批量提取API端点
   - 预期效果：获取SysDataAnno表中的所有ID并为每个ID提取数据

8. 批量提取未处理数据 (Extract Unprocessed Data)：
   - 触发条件：调用未处理数据提取API端点
   - 预期效果：获取未处理的数据记录并提取数据

9. 保存结果 (Save Result)：
   - 触发条件：数据提取完成后
   - 预期效果：将提取结果保存到数据库中

10. 合并列表结果 (Combine List Results)：
    - 触发条件：处理多个列表结果时
    - 预期效果：将多个JSON数组合并为单个扁平化列表

11. 更新令牌使用统计 (Update Token Usage)：
    - 触发条件：LLM API调用完成后
    - 预期效果：更新总计和历史令牌使用记录

12. 记录令牌使用统计 (Log Token Usage Stats)：
    - 触发条件：每分钟或LLM API调用后
    - 预期效果：记录总计、最近15分钟、最近一小时和最近一天的令牌使用情况
