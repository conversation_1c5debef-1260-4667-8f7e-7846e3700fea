package com.datayes.quarkus.service

import com.datayes.llm.DoubaoLlm
import com.datayes.llm.LlmCallLogger
import com.datayes.llm.LlmPromptBuilder
import com.datayes.llm.extractTagContent
import com.datayes.model.SysColumnType
import com.datayes.model.promptColumnMetadata
import io.quarkus.logging.Log

/**
 * PDF 结构化数据提取器
 * 负责将 PDF 文本转换为结构化 JSON 数据的工具对象
 * 提供内容分块、LLM 处理和结果合并等功能
 */
object PdfTextExtractor {

    /**
     * Splits PDF content into manageable chunks for processing
     * @param contentList List of content to be split
     * @param maxChunkSize Maximum size of each chunk (default 24000 characters)
     * @return List of content chunks, where each chunk is a list of strings
     */
    fun splitPdfContent(contentList: List<String>, maxChunkSize: Int = 24000): List<List<String>> {
        val result = mutableListOf<List<String>>()
        var currentChunk = mutableListOf<String>()
        var currentSize = 0

        for (content in contentList) {
            val contentSize = content.length
            if (currentSize + contentSize > maxChunkSize && currentChunk.isNotEmpty()) {
                result.add(currentChunk.toList())
                currentChunk = mutableListOf()
                currentSize = 0
            }
            currentChunk.add(content)
            currentSize += contentSize
        }

        if (currentChunk.isNotEmpty()) {
            result.add(currentChunk)
        }

        return result
    }

    /**
     * 处理PDF文本内容并提取结构化数据
     *
     * @param lines PDF中提取的文本行列表
     * @param columnTypes 需要提取的列类型定义
     * @param isList 指示结果是否应该是列表格式
     * @param examplePrompt 可选的示例提示，用于提高LLM的提取准确性
     * @return 提取的结构化数据，格式为JSON字符串：
     *         - 当isList=true时，返回JSON数组格式 "[{...}, {...}, ...]"
     *         - 当isList=false时，返回JSON对象格式 "{key1: value1, key2: value2, ...}"
     *         - 如果提取失败或没有找到数据，返回适当的空JSON结构("[]"或"{}")
     */
    fun parsePdfTextToJson(
        lines: List<String>,
        columnTypes: List<SysColumnType>,
        isList: Boolean = false,
        examplePrompt: String?,
        llmCallLogger: LlmCallLogger? = null,
        context: Map<String, Any>? = null,
    ): String {
        Log.debug("98e691c1 | lines: $lines")
        Log.debug("03c72c26 | columnTypes: $columnTypes")
        val joinedLines = lines.joinToString("\n")
        val needChop = joinedLines.length > 1024 * 24

        val contentChunks = if (needChop) {
            splitPdfContent(lines, 1024 * 24).map { it.joinToString("\n") }
        } else {
            listOf(joinedLines)
        }

        val columnInfos = columnTypes.promptColumnMetadata()

        // 处理需要分块且结果为列表的情况
        if (needChop && isList) {
            Log.info("Processing chopped content as list, will combine results from all chunks")
            val allResults = mutableListOf<String>()

            for (chunk in contentChunks) {
                val prompt = LlmPromptBuilder.generatePromptForText(chunk, columnInfos, true)

                val llmResponse = if (examplePrompt.isNullOrBlank().not() && examplePrompt!!.length < 1024 * 8) {
                    DoubaoLlm.sendPrompt(prompt + "\n\n\n" + examplePrompt, logger = llmCallLogger, context = context)
                } else {
                    DoubaoLlm.sendPrompt(prompt, logger = llmCallLogger, context = context)
                }

                val chunkResult = llmResponse.extractTagContent()

                if (chunkResult.isNotEmpty()) {
                    allResults.add(chunkResult)
                } else {
                    Log.warn("88d573f9 | No result found in chunk: $chunk")
                }
            }

            // 合并所有结果为单个扁平化列表
            return if (allResults.isNotEmpty()) {
                mergeJsonListResults(allResults)
            } else {
                "[]" // 未找到结果时返回空数组
            }
        }

        // 处理单一对象(非列表)或不需要分块的内容
        val extractedValues = mutableMapOf<String, String>()
        var jsonResult = ""

        for (chunk in contentChunks) {
            val prompt = if (extractedValues.isEmpty()) {
                LlmPromptBuilder.generatePromptForText(chunk, columnInfos, isList)
            } else {
                LlmPromptBuilder.generatePromptWithPreviousValuesForText(chunk, columnInfos, extractedValues)
            }

            val llmResponse = if (examplePrompt.isNullOrBlank().not() && examplePrompt!!.length < 1024 * 8) {
                DoubaoLlm.sendPrompt(prompt + "\n\n\n" + examplePrompt, logger = llmCallLogger, context = context)
            } else {
                DoubaoLlm.sendPrompt(prompt, logger = llmCallLogger, context = context)
            }

            val chunkResult = llmResponse.extractTagContent()

            if (chunkResult.isNotEmpty()) {
                extractedValues.putAll(parseExtractedValues(chunkResult))
                jsonResult = chunkResult
            } else {
                Log.warn("88d573f9 | No result found in chunk: $chunk")
            }
        }

        return jsonResult.ifEmpty { if (isList) "[]" else "{}" }
    }

    /**
     * Parse extracted values from LLM response
     */
    private fun parseExtractedValues(result: String): Map<String, String> {
        // This is a simple implementation - adjust based on your actual result format
        val valuePattern = "\"([^\"]+)\"\\s*:\\s*\"([^\"]+)\"".toRegex()
        return valuePattern.findAll(result)
            .map { it.groupValues[1] to it.groupValues[2] }
            .toMap()
    }

    /**
     * Merge multiple JSON list results into one flat list
     */
    private fun mergeJsonListResults(results: List<String>): String {
        if (results.isEmpty()) {
            return "[]"
        }

        // 简单处理：移除每个JSON数组的括号，然后将内容合并到一个新数组中
        val combined = results.joinToString(",") { result ->
            // 移除开头的 '[' 和结尾的 ']'
            result.trim().removeSurrounding("[", "]").trim()
        }

        // 确保空字符串不会导致语法错误
        val finalResult = combined.trim()
        return if (finalResult.isEmpty()) "[]" else "[$finalResult]"
    }
}