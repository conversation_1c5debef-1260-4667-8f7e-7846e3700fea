package com.datayes.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanionBase
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*

/**
 * 数据源配置表实体 (Data Source Configuration Entity)
 */
@Entity(name = "sys_data_anno")
data class SysDataAnno(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "TASK_ID")
    var taskId: Long? = null,

    // this is the expect value, conjunction of isExa eq 1
    @Column(name = "RELATION_FIELDS")
    var relationFields: String? = null,

    @Column(name = "ANNO_ID")
    var annoId: String? = null,

    @Column(name = "DB")
    var db: String? = null,

    @Column(name = "REPORT_ADDRESS")
    var reportAddress: String? = null,

    @Column(name = "CON_DATA")
    var conData: String? = null,

    // `IS_EXA` tinyint(4) DEFAULT NULL COMMENT '是否样例数据',
    @Column(name = "IS_EXA")
    var isExa: Boolean? = null
) : PanacheEntityBase {

    companion object : PanacheCompanionBase<SysDataAnno, Long> {

        fun findByTaskId(taskId: Long): List<SysDataAnno> {
            return find("taskId = ?1", taskId).list()
        }

        fun findByTaskIdAndAnnoId(taskId: Long, annoId: String): List<SysDataAnno> {
            return find("taskId = ?1 and annoId = ?2", taskId, annoId).list()
        }

        fun findByTaskIdAndIsExa(taskId: Long, isExa: Boolean): List<SysDataAnno> {
            return find("taskId = ?1 and isExa = ?2", taskId, isExa).list()
        }
    }
}
