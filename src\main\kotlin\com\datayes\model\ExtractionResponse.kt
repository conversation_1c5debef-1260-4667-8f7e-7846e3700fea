package com.datayes.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanionBase
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * 抽取预览结果 (Extraction Preview Result)
 */
@Entity(name = "extraction_responses")
data class ExtractionResponse(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    var id: Long? = null,

    @Column(name = "TASK_ID")
    var taskId: Long,

    @Column(name = "EXTRACTION_FIELDS", columnDefinition = "text")
    var extractionFields: String? = null,

    @Column(name = "REPORT_ADDRESS")
    var reportAddress: String? = null,

    @Column(name = "CREATE_BY")
    var createBy: String,

    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime = LocalDateTime.now(),

    @Column(name = "UPDATE_BY")
    var updateBy: String,

    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime = LocalDateTime.now()

) : PanacheEntityBase {

    companion object : PanacheCompanionBase<ExtractionResponse, Long> {
        fun findByTaskId(taskId: Long): List<ExtractionResponse> {
            return find("taskId", taskId).list()
        }
    }
}
