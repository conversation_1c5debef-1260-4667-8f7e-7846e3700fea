package com.datayes.llm

import java.time.LocalDateTime

/**
 * Interface for logging LLM API calls
 * Implementations of this interface handle the persistence of LLM call logs
 */
interface LlmCallLogger {
    /**
     * Log an LLM API call
     *
     * @param model The model identifier used for the call
     * @param prompt The prompt text sent to the LLM
     * @param response The response received from the LLM
     * @param timeStart The timestamp when the call started
     * @param timeEnd The timestamp when the call ended
     * @param timeCostMs The time cost of the call in milliseconds
     * @param promptTokens The number of prompt tokens used
     * @param completionTokens The number of completion tokens used
     * @param totalTokens The total number of tokens used
     * @param errorMessage Any error message that occurred during the call
     * @param finishReason The finish reason returned by the LLM
     * @param contextInfo JSON string containing additional context information
     */
    fun logLlmCall(
        model: String,
        prompt: String,
        response: String?,
        timeStart: LocalDateTime,
        timeEnd: LocalDateTime,
        timeCostMs: Long,
        promptTokens: Int?,
        completionTokens: Int?,
        totalTokens: Int?,
        errorMessage: String?,
        finishReason: String?,
        contextInfo: String? = null
    )
}