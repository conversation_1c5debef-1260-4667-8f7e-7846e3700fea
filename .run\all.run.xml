<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="all" type="JUnit" factoryName="JUnit">
    <module name="auto-data-item-extract.test" />
    <shortenClasspath name="ARGS_FILE" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="directory" />
    <dir value="$PROJECT_DIR$/src/test/kotlin" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>