# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build & Run
```bash
# Run in development mode with hot reload
./gradlew quarkusDev

# Build the application  
./gradlew quarkusBuild

# Run tests
./gradlew test

# Build native executable
./gradlew build -Dquarkus.package.type=native
```

### Testing Commands
```bash
# Run specific test class
./gradlew test --tests "com.datayes.llm.LlmPromptBuilderTest"

# Run integration tests
./gradlew test --tests "*IT"
```

### Database Schema Extraction
```bash
# Extract complete database schema for Claude Code reference
cd scripts/schema-extractor
uv run main.py
```
This generates `database_schema.json` with complete table structures, relationships, and metadata.

### Deployment
```powershell
# Deploy to staging server (requires PowerShell)
./dev.ps1 --full    # Full deployment with build
./dev.ps1 log       # View application logs
```

## Architecture Overview

This is a **<PERSON><PERSON><PERSON> + <PERSON>uarkus** application for intelligent PDF data extraction using LLM (Large Language Model) integration. The system processes PDF documents through external APIs and uses AI to extract structured data based on configurable table schemas.

### Core Components

1. **LLM Integration** (`com.datayes.llm/`)
   - `Doubao.kt`: Main LLM client with token usage tracking
   - `LlmPromptBuilder.kt`: Constructs extraction prompts from table metadata
   - `DatabaseLlmCallLogger.kt`: Logs all API calls to database

2. **PDF Processing** (`com.datayes.quarkus.service/`)
   - `PdfTableExtractor.kt`: Table-based extraction with intelligent table matching
   - `PdfTextExtractor.kt`: Text-based content extraction
   - Remote clients for PDF content and table extraction APIs

3. **Data Models** (`com.datayes.model/`)
   - JPA entities that serve as both database models and domain objects
   - `LlmCallLog.kt`: Comprehensive API call logging
   - `SysTableType.kt`, `SysColumnType.kt`: Configuration for extraction schemas

### Database Configuration
- **MySQL** connection to `************:3390/acedb`
- **Flyway** migrations in `src/main/resources/db/migration/`
- **Hibernate ORM Panache** for Kotlin with JPA entities

### External Services
- **PDF APIs**: Content extraction (`************:8000`) and table extraction (`************:9016`)
- **LLM API**: Doubao service via gateway at `llm-gateway.respool2.wmcloud-stg.com`

## Coding Style Guidelines

This project follows **extreme data-driven, Kotlin-native principles**:

### Core Rules
- **Data structures over OOP**: Use `data class` for entities, `object` for static-like functions
- **Framework isolation**: All Quarkus code in `com.datayes.quarkus` package only
- **JPA entities as domain models**: Database entities serve directly as business objects
- **Pure Kotlin core logic**: Business logic independent of frameworks for testability

### Entity Pattern
```kotlin
@Entity(name = "table_name")
data class EntityName(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    @Column(name = "COLUMN_NAME")
    var fieldName: String? = null,
    // ... other fields with nullable types and defaults
) : PanacheEntityBase()
```

### Service Pattern
```kotlin
object ServiceName {
    fun businessFunction(input: DataType): OutputType {
        // Pure Kotlin logic, no framework dependencies
        return processData(input)
    }
}
```

### REST Resource Pattern
```kotlin
@Path("/api/endpoint")
class ResourceName {
    @GET
    fun endpoint(): Response {
        val result = ServiceName.businessFunction(input)
        return Response.ok(result).build()
    }
}
```

## Key Development Notes

### LLM Integration
- All LLM calls are logged to database with token usage and timing
- Default model: `Doubao-1.5-lite-32k` via `ep-20250205111920-c8tj6`
- Cost tracking built into all API interactions

### PDF Processing Workflow
1. **Table Processing**: Extract tables via API, use LLM to match target schemas
2. **Text Processing**: Direct text extraction with structured prompts
3. **Caching**: LRU cache (max 10 items) for table extraction results
4. **Batch Processing**: Concurrent processing with configurable thread pools

### Database Logging
- `llm_call_log`: Complete audit trail of all LLM API interactions
- `llm_responses`: Extracted data results with metadata
- Context information tracked for debugging and optimization

### Configuration Files
- `application.properties`: Database, API endpoints, LLM configuration
- `gradle.properties`: JVM settings, Kotlin compiler options
- Migration scripts in `db/migration/` for schema changes

## Testing Strategy
- **Unit tests**: Pure Kotlin functions without framework dependencies
- **Integration tests**: Full workflow testing with database and external APIs
- **No mocking**: Direct testing of business logic functions
- Test naming: `*Test.kt` for unit tests, `*IT.kt` for integration tests