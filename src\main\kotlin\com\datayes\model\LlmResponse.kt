package com.datayes.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanionBase
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * LLM响应数据表实体 (LLM Response Data Entity)
 */
@Entity(name = "llm_responses")
data class LlmResponse(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "TASK_ID")
    var taskId: Long? = null,

    @Column(name = "SYS_DATA_ANNO_ID")
    var sysDataAnnoId: Long? = null,

    @Column(name = "RESPONSE_JSON", columnDefinition = "JSON")
    var responseJson: String? = null,

    @Column(name = "FILE_ADDRESS")
    var fileAddress: String? = "",

    @Column(name = "CREATE_TIME")
    var createTime: LocalDateTime? = LocalDateTime.now(),

    @Column(name = "UPDATE_TIME")
    var updateTime: LocalDateTime? = LocalDateTime.now(),
) : PanacheEntityBase {
    companion object : PanacheCompanionBase<LlmResponse, Long>
}